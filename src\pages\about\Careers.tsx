import React, { useState } from 'react';
import PageLayout from '../../components/layout/PageLayout';

const jobListings = [
  { title: 'Software Engineer', location: 'Chennai, India', description: 'Develop and maintain web applications.' },
  { title: 'Sales Manager', location: 'Mumbai, India', description: 'Lead the sales team and drive growth.' },
];

const Careers: React.FC = () => {
  const [form, setForm] = useState({ name: '', email: '', position: '', resume: null });
  const [submitted, setSubmitted] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, files } = e.target as any;
    setForm((prev) => ({
      ...prev,
      [name]: files ? files[0] : value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
    // Here you would handle sending the application data to your backend
  };

  return (
    <PageLayout hideHero hideBreadcrumbs>
      <div style={{ padding: '2rem', maxWidth: 700, margin: '0 auto' }}>
        <h1>Careers</h1>
        <h2>Current Openings</h2>
        <ul>
          {jobListings.map((job, idx) => (
            <li key={idx} style={{ marginBottom: '1.5rem' }}>
              <strong>{job.title}</strong> - {job.location}
              <div style={{ color: '#555', marginTop: '0.3rem' }}>{job.description}</div>
            </li>
          ))}
        </ul>
        <h2>Apply Now</h2>
        {submitted ? (
          <div style={{ color: 'green', marginTop: '1rem' }}>Thank you for your application!</div>
        ) : (
          <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1rem', marginTop: '1rem' }}>
            <input name="name" type="text" placeholder="Your Name" value={form.name} onChange={handleChange} required />
            <input name="email" type="email" placeholder="Your Email" value={form.email} onChange={handleChange} required />
            <select name="position" value={form.position} onChange={handleChange} required>
              <option value="">Select Position</option>
              {jobListings.map((job, idx) => (
                <option key={idx} value={job.title}>{job.title}</option>
              ))}
            </select>
            <input name="resume" type="file" accept=".pdf,.doc,.docx" onChange={handleChange} required />
            <button type="submit">Submit Application</button>
          </form>
        )}
      </div>
    </PageLayout>
  );
};

export default Careers; 