import React, { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import {
  Users,
  MapPin,
  Clock,
  Briefcase,
  Star,
  Award,
  TrendingUp,
  Heart,
  CheckCircle,
  Send,
  Upload,
  User,
  Mail,
  FileText,
  Building,
  Zap,
  Shield,
  Target,
  Globe
} from 'lucide-react';
import PageLayout from '../../components/layout/PageLayout';

// Enhanced job listings with more details
const jobListings = [
  {
    id: 1,
    title: 'Senior Software Engineer',
    location: 'Chennai, India',
    type: 'Full-time',
    experience: '3-5 years',
    department: 'Engineering',
    description: 'Develop and maintain cutting-edge web applications using modern technologies. Work with React, Node.js, and cloud platforms.',
    requirements: ['Bachelor\'s degree in Computer Science', '3+ years React experience', 'Strong problem-solving skills', 'Experience with cloud platforms'],
    benefits: ['Competitive salary', 'Health insurance', 'Flexible working hours', 'Professional development']
  },
  {
    id: 2,
    title: 'Sales Manager',
    location: 'Mumbai, India',
    type: 'Full-time',
    experience: '5-8 years',
    department: 'Sales',
    description: 'Lead our dynamic sales team and drive business growth. Develop strategic partnerships and expand market presence.',
    requirements: ['MBA or equivalent', '5+ years sales experience', 'Leadership skills', 'Industry knowledge'],
    benefits: ['Performance bonuses', 'Travel allowances', 'Team leadership opportunities', 'Career advancement']
  },
  {
    id: 3,
    title: 'Product Designer',
    location: 'Bangalore, India',
    type: 'Full-time',
    experience: '2-4 years',
    department: 'Design',
    description: 'Create intuitive and beautiful user experiences for our energy solutions platform. Work closely with engineering teams.',
    requirements: ['Design degree or portfolio', 'UI/UX experience', 'Figma/Sketch proficiency', 'User research skills'],
    benefits: ['Creative environment', 'Latest design tools', 'Conference attendance', 'Design mentorship']
  },
  {
    id: 4,
    title: 'DevOps Engineer',
    location: 'Hyderabad, India',
    type: 'Full-time',
    experience: '3-6 years',
    department: 'Engineering',
    description: 'Build and maintain robust infrastructure for our energy management systems. Ensure scalability and reliability.',
    requirements: ['DevOps experience', 'AWS/Azure knowledge', 'Docker/Kubernetes', 'CI/CD pipelines'],
    benefits: ['Remote work options', 'Certification support', 'Infrastructure budget', 'On-call compensation']
  }
];

// Company values and culture
const companyValues = [
  {
    icon: <Zap className="w-8 h-8" />,
    title: 'Innovation',
    description: 'We push boundaries in energy technology and create solutions that matter.'
  },
  {
    icon: <Shield className="w-8 h-8" />,
    title: 'Reliability',
    description: 'Our commitment to quality and dependability drives everything we do.'
  },
  {
    icon: <Target className="w-8 h-8" />,
    title: 'Excellence',
    description: 'We strive for perfection in every project and continuously improve.'
  },
  {
    icon: <Globe className="w-8 h-8" />,
    title: 'Global Impact',
    description: 'Our solutions make a difference in energy efficiency worldwide.'
  }
];

// Benefits and perks
const benefits = [
  {
    icon: <Heart className="w-6 h-6" />,
    title: 'Health & Wellness',
    description: 'Comprehensive health insurance and wellness programs'
  },
  {
    icon: <TrendingUp className="w-6 h-6" />,
    title: 'Career Growth',
    description: 'Clear advancement paths and skill development opportunities'
  },
  {
    icon: <Award className="w-6 h-6" />,
    title: 'Recognition',
    description: 'Performance-based rewards and recognition programs'
  },
  {
    icon: <Users className="w-6 h-6" />,
    title: 'Team Culture',
    description: 'Collaborative environment with diverse, talented professionals'
  },
  {
    icon: <Clock className="w-6 h-6" />,
    title: 'Work-Life Balance',
    description: 'Flexible hours and remote work options'
  },
  {
    icon: <Star className="w-6 h-6" />,
    title: 'Learning & Development',
    description: 'Continuous learning opportunities and conference attendance'
  }
];

const Careers: React.FC = () => {
  const [form, setForm] = useState({
    name: '',
    email: '',
    phone: '',
    position: '',
    experience: '',
    coverLetter: '',
    resume: null
  });
  const [submitted, setSubmitted] = useState(false);
  const [selectedJob, setSelectedJob] = useState<number | null>(null);
  const [activeSection, setActiveSection] = useState('jobs');

  const heroRef = useRef(null);
  const jobsRef = useRef(null);
  const cultureRef = useRef(null);
  const benefitsRef = useRef(null);
  const applicationRef = useRef(null);

  const isHeroInView = useInView(heroRef, { once: true });
  const isJobsInView = useInView(jobsRef, { once: true });
  const isCultureInView = useInView(cultureRef, { once: true });
  const isBenefitsInView = useInView(benefitsRef, { once: true });
  const isApplicationInView = useInView(applicationRef, { once: true });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, files } = e.target as any;
    setForm((prev) => ({
      ...prev,
      [name]: files ? files[0] : value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
    // Here you would handle sending the application data to your backend
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setActiveSection(sectionId);
    }
  };

  return (
    <PageLayout hideHero hideBreadcrumbs>
      <div className="font-['Open_Sans']">
        {/* Apply Open Sans font family consistently */}
        <style>{`
          nav.mb-10 { display: none !important; }
          .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }

          /* Apply Open Sans font family consistently */
          * {
            font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
          }

          /* Ensure proper font weights and sizes */
          h1, h2, h3, h4, h5, h6 {
            font-family: 'Open Sans', sans-serif !important;
            font-weight: 700 !important;
          }

          p, span, div {
            font-family: 'Open Sans', sans-serif !important;
          }

          button {
            font-family: 'Open Sans', sans-serif !important;
            font-weight: 600 !important;
          }
        `}</style>

        {/* Hero Section */}
        <section ref={heroRef} className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }} />
          </div>

          {/* Floating Elements */}
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-32 h-32 rounded-full bg-white/5 blur-xl"
                animate={{
                  x: [0, 100, 0],
                  y: [0, -100, 0],
                  rotate: [0, 360],
                }}
                transition={{
                  duration: 20 + i * 5,
                  repeat: Infinity,
                  ease: "linear",
                }}
                style={{
                  left: `${20 + i * 15}%`,
                  top: `${10 + i * 10}%`,
                }}
              />
            ))}
          </div>

          <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={isHeroInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8 }}
            >
              <motion.h1
                className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight"
                initial={{ opacity: 0, y: 30 }}
                animate={isHeroInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                Join Our
                <span className="block bg-gradient-to-r from-yellow-400 to-yellow-300 bg-clip-text text-transparent">
                  Innovation Journey
                </span>
              </motion.h1>

              <motion.p
                className="text-xl sm:text-2xl md:text-3xl mb-8 text-blue-100 max-w-4xl mx-auto leading-relaxed"
                initial={{ opacity: 0, y: 30 }}
                animate={isHeroInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                Shape the future of energy technology with a team that values innovation, excellence, and sustainable impact.
              </motion.p>

              <motion.div
                className="flex flex-col sm:flex-row gap-4 justify-center items-center"
                initial={{ opacity: 0, y: 30 }}
                animate={isHeroInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <button
                  onClick={() => scrollToSection('jobs')}
                  className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-blue-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-300 hover:to-yellow-400 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  <Briefcase className="w-5 h-5 inline mr-2" />
                  View Open Positions
                </button>
                <button
                  onClick={() => scrollToSection('culture')}
                  className="border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-blue-900 transition-all duration-300 transform hover:scale-105"
                >
                  <Users className="w-5 h-5 inline mr-2" />
                  Our Culture
                </button>
              </motion.div>
            </motion.div>

            {/* Stats */}
            <motion.div
              className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-white/20"
              initial={{ opacity: 0, y: 30 }}
              animate={isHeroInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              {[
                { number: '500+', label: 'Team Members' },
                { number: '15+', label: 'Countries' },
                { number: '25+', label: 'Years Experience' },
                { number: '1000+', label: 'Projects Delivered' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-yellow-400 mb-2">{stat.number}</div>
                  <div className="text-blue-200 text-sm md:text-base">{stat.label}</div>
                </div>
              ))}
            </motion.div>
          </div>

          {/* Scroll Indicator */}
          <motion.div
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            <div className="flex flex-col items-center cursor-pointer" onClick={() => scrollToSection('jobs')}>
              <span className="text-sm mb-2">Explore Opportunities</span>
              <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
                <div className="w-1 h-3 bg-white rounded-full mt-2 animate-bounce"></div>
              </div>
            </div>
          </motion.div>
        </section>

        {/* Company Culture Section */}
        <section id="culture" ref={cultureRef} className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isCultureInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <div className="inline-block bg-blue-100 text-blue-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                OUR CULTURE
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Values That Drive Us Forward
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                We believe in creating an environment where innovation thrives, people grow, and meaningful impact is made every day.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {companyValues.map((value, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  animate={isCultureInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 group"
                >
                  <div className="bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl p-4 w-fit mb-6 group-hover:from-blue-700 group-hover:to-blue-800 transition-all duration-300">
                    <div className="text-white">{value.icon}</div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{value.description}</p>
                </motion.div>
              ))}
            </div>

            {/* Culture Highlights */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isCultureInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="mt-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-3xl p-8 md:p-12 text-white"
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-3xl md:text-4xl font-bold mb-6">
                    Why Choose Atandra Energy?
                  </h3>
                  <div className="space-y-4">
                    {[
                      'Work on cutting-edge energy solutions that impact millions',
                      'Collaborate with industry experts and thought leaders',
                      'Access to latest technologies and professional development',
                      'Flexible work environment with global opportunities',
                      'Competitive compensation and comprehensive benefits'
                    ].map((item, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <CheckCircle className="w-6 h-6 text-yellow-400 flex-shrink-0 mt-0.5" />
                        <span className="text-lg">{item}</span>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="relative">
                  <div className="bg-white/10 rounded-2xl p-8 backdrop-blur-sm">
                    <div className="text-center">
                      <div className="text-4xl md:text-5xl font-bold text-yellow-400 mb-2">4.8/5</div>
                      <div className="text-xl mb-4">Employee Satisfaction</div>
                      <div className="flex justify-center space-x-1 mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <p className="text-blue-100">Based on internal surveys and feedback</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Job Listings Section */}
        <section id="jobs" ref={jobsRef} className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isJobsInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <div className="inline-block bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                OPEN POSITIONS
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Find Your Perfect Role
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Explore exciting opportunities to grow your career with us. We're always looking for talented individuals who share our passion for innovation.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {jobListings.map((job, index) => (
                <motion.div
                  key={job.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={isJobsInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100 group"
                >
                  <div className="flex justify-between items-start mb-6">
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                        {job.title}
                      </h3>
                      <div className="flex flex-wrap gap-3 text-sm">
                        <span className="flex items-center text-gray-600">
                          <MapPin className="w-4 h-4 mr-1" />
                          {job.location}
                        </span>
                        <span className="flex items-center text-gray-600">
                          <Clock className="w-4 h-4 mr-1" />
                          {job.type}
                        </span>
                        <span className="flex items-center text-gray-600">
                          <Briefcase className="w-4 h-4 mr-1" />
                          {job.experience}
                        </span>
                      </div>
                    </div>
                    <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">
                      {job.department}
                    </span>
                  </div>

                  <p className="text-gray-700 mb-6 leading-relaxed">{job.description}</p>

                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Key Requirements:</h4>
                    <ul className="space-y-2">
                      {job.requirements.slice(0, 3).map((req, reqIndex) => (
                        <li key={reqIndex} className="flex items-start space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                          <span className="text-gray-600 text-sm">{req}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex justify-between items-center">
                    <button
                      onClick={() => setSelectedJob(selectedJob === job.id ? null : job.id)}
                      className="text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-300"
                    >
                      {selectedJob === job.id ? 'Show Less' : 'View Details'}
                    </button>
                    <button
                      onClick={() => {
                        setForm(prev => ({ ...prev, position: job.title }));
                        scrollToSection('application');
                      }}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-full font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105"
                    >
                      Apply Now
                    </button>
                  </div>

                  {/* Expanded Details */}
                  {selectedJob === job.id && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="mt-6 pt-6 border-t border-gray-200"
                    >
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3">All Requirements:</h4>
                          <ul className="space-y-2">
                            {job.requirements.map((req, reqIndex) => (
                              <li key={reqIndex} className="flex items-start space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                                <span className="text-gray-600 text-sm">{req}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3">Benefits:</h4>
                          <ul className="space-y-2">
                            {job.benefits.map((benefit, benefitIndex) => (
                              <li key={benefitIndex} className="flex items-start space-x-2">
                                <Star className="w-4 h-4 text-yellow-500 flex-shrink-0 mt-0.5" />
                                <span className="text-gray-600 text-sm">{benefit}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isJobsInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-center mt-16"
            >
              <div className="bg-gradient-to-r from-gray-50 to-white rounded-2xl p-8 border border-gray-200">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Don't see the perfect role?
                </h3>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  We're always interested in connecting with talented individuals. Send us your resume and let us know how you'd like to contribute to our mission.
                </p>
                <button
                  onClick={() => scrollToSection('application')}
                  className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-blue-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-300 hover:to-yellow-400 transition-all duration-300 transform hover:scale-105"
                >
                  Submit General Application
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Benefits Section */}
        <section id="benefits" ref={benefitsRef} className="py-20 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isBenefitsInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <div className="inline-block bg-green-100 text-green-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                BENEFITS & PERKS
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                More Than Just A Job
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                We believe in taking care of our team members with comprehensive benefits and a supportive work environment.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  animate={isBenefitsInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100 group"
                >
                  <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-3 w-fit mb-4 group-hover:from-green-600 group-hover:to-green-700 transition-all duration-300">
                    <div className="text-white">{benefit.icon}</div>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{benefit.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{benefit.description}</p>
                </motion.div>
              ))}
            </div>

            {/* Additional Benefits Highlight */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isBenefitsInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="mt-16 grid grid-cols-1 lg:grid-cols-3 gap-8"
            >
              <div className="lg:col-span-2 bg-gradient-to-r from-green-600 to-green-700 rounded-2xl p-8 text-white">
                <h3 className="text-2xl md:text-3xl font-bold mb-6">
                  Comprehensive Package
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Financial Benefits</h4>
                    <ul className="space-y-2 text-green-100">
                      <li>• Competitive salary packages</li>
                      <li>• Performance-based bonuses</li>
                      <li>• Stock options for senior roles</li>
                      <li>• Retirement savings plans</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Work-Life Balance</h4>
                    <ul className="space-y-2 text-green-100">
                      <li>• Flexible working hours</li>
                      <li>• Remote work options</li>
                      <li>• Generous vacation policy</li>
                      <li>• Mental health support</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
                <div className="text-center">
                  <div className="bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full p-4 w-fit mx-auto mb-4">
                    <Award className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    Recognition Program
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Monthly and quarterly awards for outstanding performance and innovation.
                  </p>
                  <div className="text-2xl font-bold text-yellow-600">₹50K+</div>
                  <div className="text-sm text-gray-500">Average annual rewards</div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Application Form Section */}
        <section id="application" ref={applicationRef} className="py-20 bg-gradient-to-br from-gray-900 to-gray-800">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isApplicationInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8 }}
              className="text-center mb-12"
            >
              <div className="inline-block bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                JOIN OUR TEAM
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
                Ready to Make an Impact?
              </h2>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
                Take the first step towards an exciting career with us. We'd love to hear from you!
              </p>
            </motion.div>

            {submitted ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="bg-gradient-to-r from-green-500 to-green-600 rounded-2xl p-8 text-center text-white"
              >
                <CheckCircle className="w-16 h-16 mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4">Application Submitted Successfully!</h3>
                <p className="text-green-100 mb-6">
                  Thank you for your interest in joining our team. We'll review your application and get back to you within 5-7 business days.
                </p>
                <button
                  onClick={() => {
                    setSubmitted(false);
                    setForm({ name: '', email: '', phone: '', position: '', experience: '', coverLetter: '', resume: null });
                  }}
                  className="bg-white text-green-600 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300"
                >
                  Submit Another Application
                </button>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={isApplicationInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="bg-white rounded-2xl p-8 shadow-2xl"
              >
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        <User className="w-4 h-4 inline mr-2" />
                        Full Name *
                      </label>
                      <input
                        name="name"
                        type="text"
                        placeholder="Enter your full name"
                        value={form.name}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        <Mail className="w-4 h-4 inline mr-2" />
                        Email Address *
                      </label>
                      <input
                        name="email"
                        type="email"
                        placeholder="Enter your email"
                        value={form.email}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <input
                        name="phone"
                        type="tel"
                        placeholder="Enter your phone number"
                        value={form.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        <Briefcase className="w-4 h-4 inline mr-2" />
                        Position *
                      </label>
                      <select
                        name="position"
                        value={form.position}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                      >
                        <option value="">Select Position</option>
                        {jobListings.map((job) => (
                          <option key={job.id} value={job.title}>{job.title}</option>
                        ))}
                        <option value="General Application">General Application</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Years of Experience
                    </label>
                    <select
                      name="experience"
                      value={form.experience}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    >
                      <option value="">Select experience level</option>
                      <option value="0-1">0-1 years (Entry Level)</option>
                      <option value="2-3">2-3 years</option>
                      <option value="4-6">4-6 years</option>
                      <option value="7-10">7-10 years</option>
                      <option value="10+">10+ years (Senior Level)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <FileText className="w-4 h-4 inline mr-2" />
                      Cover Letter
                    </label>
                    <textarea
                      name="coverLetter"
                      placeholder="Tell us why you're interested in this position and what makes you a great fit..."
                      value={form.coverLetter}
                      onChange={handleChange}
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 resize-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <Upload className="w-4 h-4 inline mr-2" />
                      Resume/CV *
                    </label>
                    <div className="relative">
                      <input
                        name="resume"
                        type="file"
                        accept=".pdf,.doc,.docx"
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                    </div>
                    <p className="text-sm text-gray-500 mt-2">
                      Accepted formats: PDF, DOC, DOCX (Max size: 5MB)
                    </p>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 px-8 rounded-lg font-bold text-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    <Send className="w-5 h-5 inline mr-2" />
                    Submit Application
                  </button>
                </form>
              </motion.div>
            )}
          </div>
        </section>

        {/* Footer CTA */}
        <section className="py-16 bg-gradient-to-r from-yellow-400 to-yellow-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-blue-900 mb-4">
              Questions About Working With Us?
            </h2>
            <p className="text-xl text-blue-800 mb-8">
              Our HR team is here to help you understand more about opportunities at Atandra Energy.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="bg-blue-900 text-white px-8 py-4 rounded-full font-bold hover:bg-blue-800 transition-all duration-300 transform hover:scale-105"
              >
                <Mail className="w-5 h-5 inline mr-2" />
                <EMAIL>
              </a>
              <a
                href="tel:+911234567890"
                className="border-2 border-blue-900 text-blue-900 px-8 py-4 rounded-full font-bold hover:bg-blue-900 hover:text-white transition-all duration-300 transform hover:scale-105"
              >
                +91 12345 67890
              </a>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  );
};

export default Careers;