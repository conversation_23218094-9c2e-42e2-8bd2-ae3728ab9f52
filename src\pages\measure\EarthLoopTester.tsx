import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from "framer-motion";
import {
  ArrowRight,
  Zap,
  Shield,
  Gauge,
  FileText,
  Menu,
  X,
  ChevronRight,
  Check
} from "lucide-react";
import PageLayout from "@/components/layout/PageLayout";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const PDF_URL = "/T&M April 2025.pdf";

const earthLoopTesters = [
  {
    id: "CA-6417",
    name: "Earth Loop Tester CA 6417",
    description: "Advanced earth loop tester with OLED display and wireless communication for efficient ground testing. Features non-invasive clamp-on technology for quick and easy measurements without disconnecting the ground system.",
    features: [
      "Ø35mm clamping diameter",
      "Large, 152-segment multi-function OLED display",
      "Display Counts: 1,500 counts (Loop ohmmeter) & 4,000-count display (Ammeter)",
      "Alarms",
      "HOLD & PRE-HOLD",
      "Auto power off",
      "Memory: 2,000 measurements",
      "Communication: Bluetooth",
      "PC interface & Android app"
    ],
    measurements: [
      "Loop Resistance Range: Upto 1,500 Ω",
      "Accuracy: ±1.5% ±0.01",
      "Frequency 2,083 Hz (Measurement frequency)",
      "50, 60, 128 or 2,083 Hz (Transposition freq.)",
      "Loop Inductance Range: Upto 500μH",
      "Accuracy: ±3% ±r",
      "Ground Voltage: Upto 75 V",
      "Current Range: Upto 39.99 A",
      "Accuracy: ±2% ±r"
    ],
    image: "/earth_loop_testers/CA 6417.png",
    category: "Earth Loop Tester",
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  },
  {
    id: "CA-6418",
    name: "Earth Loop Tester CA 6418",
    description: "Professional earth loop tester with oblong head design for enhanced accessibility and measurement accuracy. Perfect for testing grounding systems in tight spaces and complex installations.",
    features: [
      "Ø32/55mm max. clamping diameter (Oblong head)",
      "Large, 152-segment multi-function OLED display",
      "Alarms",
      "Automatic calibration of the jaw gap",
      "HOLD: Manual or automatic PRE-Hold",
      "Auto power off",
      "Memory: 300 measurements (Time/date-stamped)"
    ],
    measurements: [
      "Loop Resistance Range: Upto 1,200 Ω",
      "Accuracy: ±(1.5%R + 2r)",
      "Current Range: Upto 20 A",
      "Accuracy: ±(2 %R + 200μA)",
      "Ground Voltage Range: Upto 75 V"
    ],
    image: "/earth_loop_testers/CA 6418.png",
    category: "Earth Loop Tester",
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  }
];

const tabs = [
  { id: 'overview', label: 'Overview', icon: Gauge }
];

const HeroSection = ({ onRequestDemo, onViewBrochure }) => (
  <section className="relative min-h-[60vh] flex items-center justify-center py-6 md:py-12 overflow-hidden">
    <div className="absolute inset-0 z-0">
      <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
      <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      <div className="absolute top-20 right-20 w-32 h-32 bg-yellow-300 rounded-full opacity-20"></div>
    </div>
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 flex flex-col items-center justify-center w-full">
      <div className="text-center w-full">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8 flex flex-col items-center justify-center w-full"
        >
          <div className="inline-block bg-yellow-400 px-6 py-3 rounded-full mb-4">
            <span className="text-gray-900 font-bold text-lg" style={{ fontFamily: 'Open Sans, sans-serif' }}>
              KRYKARD Earth Loop Testers
            </span>
          </div>
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-bold text-gray-900 leading-tight mb-4" style={{ fontFamily: 'Open Sans, sans-serif' }}>
            EARTH LOOP <span className="text-gray-900">TESTERS</span>
          </h1>
          <p className="text-base md:text-lg lg:text-lg text-black leading-relaxed font-medium max-w-4xl mx-auto mb-8" style={{ fontFamily: 'Open Sans, sans-serif' }}>
            Non-invasive measurement technology for efficient ground resistance testing without disconnecting the ground system.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center pt-2">
            <Button
              className="w-full sm:w-auto px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base"
              onClick={onRequestDemo}
            >
              <span>Request Demo</span>
              <ArrowRight className="h-5 w-5" />
            </Button>
            <Button
              className="w-full sm:w-auto px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:bg-yellow-50 flex items-center justify-center space-x-3 text-base"
              onClick={onViewBrochure}
            >
              <span>View Brochure</span>
              <FileText className="h-5 w-5" />
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  </section>
);

const FeatureHighlight = ({ icon: Icon, title, description }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6 }}
    viewport={{ once: true }}
    className="rounded-2xl border border-yellow-200 hover:border-yellow-400 transition-all duration-300 p-4 h-full bg-transparent flex flex-col items-center text-center"
    style={{ fontFamily: 'Open Sans, sans-serif' }}
  >
    <div className="flex flex-row items-center gap-3 mb-2 justify-center w-full">
      <div className="bg-gradient-to-br from-yellow-400 to-yellow-500 w-10 h-10 rounded-2xl flex items-center justify-center">
        <Icon className="h-6 w-6 text-gray-900" />
      </div>
      <h3 className="text-base md:text-lg font-bold text-gray-900 m-0 p-0">{title}</h3>
    </div>
    <p className="text-gray-700 font-medium text-sm md:text-base leading-relaxed">{description}</p>
  </motion.div>
);

// Product Card Component (copied and adapted from Multimeters.tsx)
const ProductCard = ({
  title,
  modelNumber,
  image,
  displayInfo,
  onViewDetailsClick
}: {
  title: string;
  modelNumber: string;
  image: string;
  displayInfo: string;
  onViewDetailsClick: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white border border-yellow-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 flex flex-col h-full"
      style={{ fontFamily: 'Open Sans, sans-serif' }}
    >
      {/* Model Number Badge */}
      <div className="flex justify-end p-3">
        <span className="bg-yellow-100 text-yellow-800 text-xs font-semibold px-3 py-1 rounded-full">
          {modelNumber}
        </span>
      </div>
      {/* Product Image */}
      <div className="flex items-center justify-center h-32 md:h-40 bg-yellow-50">
        <img
          src={image}
          alt={title}
          className="max-h-full max-w-full object-contain"
          onError={e => {
            e.currentTarget.onerror = null;
            e.currentTarget.src = 'https://via.placeholder.com/200x150/FFD700/000000?text=No+Image';
          }}
        />
      </div>
      <div className="p-4 flex-1 flex flex-col justify-between">
        <div className="text-center mb-2">
          <h3 className="text-base font-bold text-gray-900">{title}</h3>
          <div className="text-xs text-gray-600 mt-1">{displayInfo}</div>
        </div>
        <Button
          onClick={onViewDetailsClick}
          className="w-full bg-yellow-300 hover:bg-yellow-400 text-gray-900 font-bold py-2 px-4 rounded-lg transition-all duration-200 mt-4 flex items-center justify-center space-x-2"
        >
          <span>View Details</span>
          <ChevronRight className="inline h-4 w-4 ml-1" />
        </Button>
      </div>
    </motion.div>
  );
};

const Navigation = ({ activeTab, setActiveTab, isMobileMenuOpen, setIsMobileMenuOpen }) => (
  <nav className="sticky top-0 z-50 bg-white shadow-lg border-b border-gray-200" style={{ fontFamily: 'Open Sans, sans-serif' }}>
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="hidden md:flex justify-center py-4">
        <div className="bg-gray-100 p-2 rounded-full flex space-x-2">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-6 py-3 font-bold rounded-full transition-all duration-300 flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'bg-yellow-400 text-gray-900 shadow-lg transform -translate-y-0.5'
                  : 'text-gray-600 hover:bg-yellow-50 hover:text-yellow-600'
              }`}
            >
              {React.createElement(tab.icon, { className: 'h-5 w-5' })}
              <span className="text-base">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>
      <div className="md:hidden flex justify-between items-center py-4">
        <span className="font-bold text-gray-900 text-lg">
          {tabs.find(tab => tab.id === activeTab)?.label}
        </span>
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="text-gray-600 hover:text-yellow-600"
        >
          {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-white border-t border-gray-200"
          >
            <div className="py-4 space-y-2">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center space-x-3 ${
                    activeTab === tab.id
                      ? 'bg-yellow-100 text-yellow-800 font-bold'
                      : 'text-gray-600 hover:bg-yellow-50 hover:text-yellow-600'
                  }`}
                >
                  {React.createElement(tab.icon, { className: 'h-5 w-5' })}
                  <span className="text-base">{tab.label}</span>
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  </nav>
);

const ContactSection = ({ onContactClick }) => (
  <section className="py-12 md:py-16 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200 mt-10 mb-24">
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
      >
        <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
          Need Expert Advice?
        </h2>
        <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
          Our specialists provide comprehensive guidance on earth loop testing solutions
        </p>
        <Button
          className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto mt-2"
          onClick={onContactClick}
        >
          <span>Contact Sales</span>
          <ArrowRight className="h-5 w-5" />
        </Button>
      </motion.div>
    </div>
  </section>
);

const EarthLoopTesters = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const params = new URLSearchParams(location.search);
  const selectedProductId = params.get('product') || 'CA-6417';
  const detailTab = params.get('detailTab') || 'features';


  // Effect to check URL parameters for initial tab and product type
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    const product = params.get('product');

    if (tab) setActiveTab(tab);
    if (product) {
      const foundProduct = earthLoopTesters.find(p => p.id === product);
      if (foundProduct) {
        // If we're showing details, scroll to the product details after a short delay
        if (tab === "details") {
          setTimeout(() => {
            const productDetailsElement = document.getElementById("product-details-section");
            if (productDetailsElement) {
              productDetailsElement.scrollIntoView({ behavior: 'auto', block: 'start' });
            }
          }, 100);
        }
      }
    }
  }, [location]);

  // Handlers
  const handleRequestDemo = () => navigate('/contact/sales');
  const handleViewBrochure = () => window.open(PDF_URL, '_blank');

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      <div className="font-['Open_Sans']">
      {/* Modern Background */}
      {/* Hero Section */}
      <HeroSection onRequestDemo={handleRequestDemo} onViewBrochure={handleViewBrochure} />

      {/* Products Section - moved up */}
      <section className="min-h-screen flex items-center justify-center bg-yellow-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-10 w-full"
          >
            <div className="inline-block bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
              PRODUCTS
            </div>
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Our Earth Loop Tester Range
            </h2>
            <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-2">
              Choose the perfect earth loop tester for your grounding measurement needs.
            </p>
          </motion.div>
          {/* Responsive Product Card Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {earthLoopTesters.map((product) => (
              <ProductCard
                key={product.id}
                title={product.name}
                modelNumber={product.id}
                image={product.image}
                displayInfo={product.features[0]}
                onViewDetailsClick={() => navigate(`/measure/earth-loop-testers/product/${product.id}`)}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Key Features Section - moved below Products */}
          <div className="py-8 md:py-12 bg-yellow-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-8 lg:px-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-14"
              >
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-gray-900 mb-4 tracking-tight">
                  Key Features
                </h2>
                <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto font-medium mb-2">
                  Discover the standout features that make our earth loop testers the preferred choice for professionals.
                </p>
              </motion.div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 justify-center">
                <FeatureHighlight
                  icon={Zap}
                  title="Non-Invasive Testing"
                  description="Measure earth resistance without disconnecting the ground system, saving time and maintaining safety."
                />
                <FeatureHighlight
                  icon={Gauge}
                  title="High Precision"
                  description="Advanced clamp-on technology provides accurate measurements of loop resistance with industry-leading precision."
                />
                <FeatureHighlight
                  icon={Shield}
                  title="Safety & Compliance"
                  description="Test and verify grounding systems for compliance with safety standards."
                />
              </div>
            </div>
          </div>



      {activeTab === 'overview' && (
        <>
          {/* Contact Section */}
          <ContactSection onContactClick={handleRequestDemo} />
        </>
      )}

      {activeTab === "details" && (
        <div id="product-detail-section" className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
          {/* Enhanced Product Type Selector - Compact Version */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-yellow-50 to-white rounded-xl shadow-md p-4 mb-6 relative overflow-hidden"
          >
            <div className="absolute top-0 right-0 w-32 md:w-64 h-32 md:h-64 bg-yellow-200 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2 blur-3xl"></div>

            <h2 className="text-base md:text-lg lg:text-xl font-bold text-gray-900 mb-3 relative z-10 text-center md:text-left font-['Open_Sans']">
              Select <span className="text-gray-900">Earth Loop Tester</span> Model
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 relative z-10">
              {earthLoopTesters.map((tester) => (
                <motion.button
                  key={tester.id}
                  whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ y: 0 }}
                  onClick={() => {
                    navigate(`?tab=details&product=${tester.id}`, { replace: true });
                  }}
                  className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                    tester.id === selectedProductId
                      ? "ring-1 ring-yellow-400"
                      : "hover:ring-1 hover:ring-yellow-400"
                  }`}
                >
                  <div className={`h-full py-3 px-3 flex flex-col items-center text-center ${
                    tester.id === selectedProductId
                      ? "bg-yellow-400 text-black"
                      : "bg-white hover:bg-yellow-100"
                  }`}>
                    <div className="mb-2">
                      <Gauge className={`h-6 md:h-8 w-6 md:w-8 ${tester.id === selectedProductId ? "text-black" : "text-yellow-400"}`} />
                    </div>

                    <h3 className={`text-sm md:text-base lg:text-lg font-bold mb-1 font-['Open_Sans'] ${tester.id === selectedProductId ? "text-black" : "text-gray-900"}`}>
                      {tester.id}
                    </h3>

                    <div className={`text-xs md:text-sm font-['Open_Sans'] ${tester.id === selectedProductId ? "text-black opacity-80" : "text-gray-500"}`}>
                      {tester.features[0]}
                    </div>

                    {tester.id === selectedProductId && (
                      <div className="mt-2 bg-white bg-opacity-20 rounded-full px-3 py-0.5 text-xs md:text-sm font-semibold">
                        Selected
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Enhanced Product Detail Section */}
          {earthLoopTesters.find(p => p.id === selectedProductId) && ( // Assuming CA-6417 is selected by default or based on URL
            <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
              <div className="md:col-span-5">
                <div className="sticky top-24">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    className="bg-gradient-to-br from-white to-yellow-50 rounded-2xl shadow-lg p-6 mb-4 relative overflow-hidden"
                  >
                    {/* Background decoration */}
                    <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-yellow-200 rounded-full opacity-10 blur-3xl"></div>
                    <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-100 rounded-full opacity-10 blur-3xl"></div>

                    {/* Product badge */}
                    <div className="absolute top-4 left-4 bg-yellow-400 text-white px-3 py-1 rounded-full text-sm font-semibold z-10">
                      {earthLoopTesters.find(p => p.id === selectedProductId)?.id}
                    </div>

                    {/* Image container with glow effect */}
                    <div className="relative mb-6 mt-3">
                      <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-xl transform scale-90"></div>
                      <motion.div
                        animate={{ y: [0, -10, 0] }}
                        transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
                        className="relative z-10 flex justify-center items-center py-6"
                      >
                        <img
                          src={earthLoopTesters.find(p => p.id === selectedProductId)?.image}
                          alt={earthLoopTesters.find(p => p.id === selectedProductId)?.name}
                          className="max-h-56 w-auto object-contain drop-shadow-2xl transform transition-transform duration-500 hover:scale-110"
                        />
                      </motion.div>
                    </div>

                    {/* Product details */}
                    <div className="text-center mb-6">
                      <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2 font-['Open_Sans']">{earthLoopTesters.find(p => p.id === selectedProductId)?.name}</h3>
                      <div className="flex justify-center space-x-3 mb-4">
                        {earthLoopTesters.find(p => p.id === selectedProductId)?.features.slice(0, 1).map((feature, idx) => (
                          <span key={idx} className="inline-block bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full text-sm font-medium font-['Open_Sans']">
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Specs cards */}
                    <div className="grid grid-cols-2 gap-3 mb-6">
                      <div className="bg-white p-3 rounded-xl shadow-sm border border-yellow-100 transform transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
                        <div className="flex items-center mb-2">
                          <Gauge className="h-5 w-5 text-yellow-400 mr-2" />
                          <span className="text-sm font-medium text-gray-500 font-['Open_Sans']">Clamping Diameter</span>
                        </div>
                        <span className="font-semibold text-gray-900 text-base font-['Open_Sans']">{earthLoopTesters.find(p => p.id === selectedProductId)?.measurements[0].split(':')[1]}</span>
                      </div>
                      <div className="bg-white p-3 rounded-xl shadow-sm border border-yellow-100 transform transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
                        <div className="flex items-center mb-2">
                          <Zap className="h-5 w-5 text-yellow-400 mr-2" />
                          <span className="text-sm font-medium text-gray-500 font-['Open_Sans']">Display</span>
                        </div>
                        <span className="font-semibold text-gray-900 text-base font-['Open_Sans']">OLED, 152-segment</span>
                      </div>
                    </div>

                    {/* View Brochure button */}
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        className="w-full bg-gradient-to-r from-yellow-400 to-yellow-400 hover:from-yellow-400 hover:to-yellow-400 text-gray-900 font-semibold rounded-lg shadow-md transition-all duration-300 flex items-center justify-center py-3 text-base"
                        onClick={handleViewBrochure}
                      >
                        <span>View Product Brochure</span>
                        <FileText className="ml-2 h-5 w-5" />
                      </Button>
                    </motion.div>
                  </motion.div>
                </div>
              </div>

              <div className="md:col-span-7">
                {/* Enhanced Detail Tabs Navigation */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="bg-white rounded-xl shadow-lg mb-6 overflow-hidden"
                >
                  <div className="flex border-b overflow-x-auto scrollbar-hide">
                    {[
                      { id: "features", label: "Features", icon: Shield },
                      { id: "measurements", label: "Measurements", icon: Gauge }
                    ].map((tab) => (
                      <Button
                        key={tab.id}
                        onClick={() => {
                          navigate(`?tab=details&product=${selectedProductId}&detailTab=${tab.id}`, { replace: true });
                        }}
                        className={`px-3 sm:px-4 py-3 font-medium whitespace-nowrap flex items-center transition-all duration-300 text-sm sm:text-base min-w-fit ${
                          detailTab === tab.id
                            ? "bg-yellow-50 border-b-2 border-yellow-400 text-yellow-700"
                            : "text-gray-700 hover:text-yellow-600 hover:bg-yellow-100"
                        }`}
                      >
                        <span className="mr-1 sm:mr-2">{React.createElement(tab.icon, { className: 'h-5 w-5' })}
                        </span>
                        <span className="hidden xs:inline sm:inline">{tab.label}</span>
                        <span className="xs:hidden sm:hidden">{tab.label.slice(0, 4)}</span>
                      </Button>
                    ))}
                  </div>
                </motion.div>

                {/* Tab Content with enhanced styling */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="transform origin-top"
                >
                  {/* ProductTabContent component was removed, so this section is now empty */}
                </motion.div>
              </div>
            </div>
          )}

          {/* Enhanced Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="mt-12"
          >
            <ContactSection onContactClick={handleRequestDemo} />
          </motion.div>


        </div>
      )}

      {/* No PDF Viewer Modal - Using direct link instead */}
      </div>
    </PageLayout>
  );
};

export default EarthLoopTesters;