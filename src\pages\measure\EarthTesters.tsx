import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowRight,
  Zap,
  Shield,
  Gauge,
  FileText,
  Menu,
  X,
  ChevronRight,
  Star
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';

const earthTesters = [
  {
    id: 'ca-6424',
    title: '2P/3P Earth Tester',
    modelNumber: 'CA 6424',
    image: '/earth testers/CA 6424.png',
    displayInfo: '600 V',
    model: 'CA 6424',
    subtitle: '2P/3P Earth Tester',
    voltage: '600 V',
    measurement: '2P/3P',
    accuracy: '±2%',
    features: [
      'Backlit custom 206-segment LCD',
      'Auto Power off',
      'Noise indication',
      'Measurement Mode: V, I, R 2P (Ω), R 3P (Ω)'
    ],
    specs: [
      'Voltage: Upto 600 V',
      '2P earth resistance/accuracy: 0.05 Ω to 50 kΩ/± (2%R +1count)',
      '3P earth resistance/accuracy: 0.5 Ω-50.00 kΩ/± (2%R +1count)',
      'RH stake resistance: 0.05 Ω to 49.99 kΩ',
      'U₀ voltage measurement: Upto 600 VAC',
      'Leakage current: Upto 60.00 A'
    ],
    applications: [
      'Ground system verification',
      'Lightning protection system testing',
      'Utility and substation testing',
      'Construction site safety'
    ]
  },
  {
    id: 'ca-6460',
    title: '4P Earth Tester',
    modelNumber: 'CA 6460',
    image: '/earth testers/CA 6460.png',
    displayInfo: '2,000 Ω',
    model: 'CA 6460',
    subtitle: '4P Earth Tester',
    voltage: '2,000 Ω',
    measurement: '4P',
    accuracy: '±2%',
    features: [
      'Large backlit digital display with 2,000 counts',
      '3 fault presence indicators to validate measurement',
      'Battery',
      'Non Rechargeable/ Rechargeable Batteries'
    ],
    specs: [
      '3-in-1 tester',
      'Resistivity: ("Wenner method" (4-rod method))',
      'Ground Resistance: ("TAGG method" (62% method))',
      'Resistance Range: 0.01 to 2,000 Ω (3 automatic ranges)',
      'Test current: 10mA, 1mA, 0.1mA',
      'Accuracy: ±2% ±1point',
      'Frequency: 128Hz'
    ],
    applications: [
      'Ground system verification',
      'Industrial plant maintenance',
      'Lightning protection system testing',
      'Utility and substation testing'
    ]
  },
  {
    id: 'ca-6470n',
    title: '3P/4P Earth Tester',
    modelNumber: 'CA 6470N',
    image: '/earth testers/CA 6470N.png',
    displayInfo: '99.9 kΩ',
    model: 'CA 6470N',
    subtitle: '3P/4P Earth Tester',
    voltage: '99.9 kΩ',
    measurement: '3P/4P',
    accuracy: '±2%',
    features: [
      'Backlit LCD display featuring (3 simultaneous display levels)',
      'Noise interference detection',
      'Alarm function',
      'Memory: 512 memory locations',
      'Communication: USB'
    ],
    specs: [
      '4-in-1 tester (CA 6470N): Earth, Resistivity, Coupling, Continuity',
      '3-pole, 4-pole measurements Range: 0.01 Ω to 99.9 kΩ',
      'Frequency: 41 to 512 Hz',
      'Selective 4-pole measurements, 2 clamps (CA 6471)',
      'Range: 0.01 Ω to 500 Ω',
      'Frequency: 128 Hz, 1367 Hz, 1611 Hz, 1758 Hz',
      'Resistivity Range: 0.01 Ω to 99.9 kΩ',
      'Frequency: 128 Hz'
    ],
    applications: [
      'Ground system verification',
      'Industrial plant maintenance',
      'Lightning protection system testing',
      'Utility and substation testing'
    ]
  },
  {
    id: 'ca-6472',
    model: 'CA 6472',
    subtitle: 'Advanced 3P/4P Earth Tester',
    image: '/earth testers/CA 6472.png',
    voltage: '99.9 kΩ',
    measurement: '3P/4P',
    accuracy: '±2%',
    features: [
      'Backlit LCD display featuring (3 simultaneous display levels)',
      'Automatic & Expert mode',
      'Earth measurement on Pylons with earth cable (with CA 6474 option)',
      'Alarm function',
      'Memory: 512-record memory',
      'Communication: USB',
      'Measurement with CA 6474 Range: 0.001 Ω to 99.9 kΩ',
      'Frequency: 41 to 5078 Hz'
    ],
    specs: [
      '3P, 4P/4P Selective Method: Range: 0.01 Ω to 99.9 kΩ',
      'Frequency: 41 to 5,078 Hz',
      'Earth Measurement (2 clamps) Range: 0.01 Ω to 500 Ω',
      'Frequency: 1,367 Hz (auto) & 1,367 Hz, 1,611 Hz, 1,758 Hz (manual)',
      'Resistivity: Range: 0.01 Ω to 99.9 kΩ',
      'Frequency: 41 to 128 Hz',
      'Earth Potential Range: 0.01 mV to 65.00 V',
      'Frequency: 41 to 128 Hz',
      'DC Resistance Range: 0.001 Ω to 99.9 kΩ'
    ],
    applications: [
      'Research and development',
      'Utility and substation testing',
      'Industrial plant maintenance',
      'Lightning protection system testing'
    ]
  }
];

const tabs = [
  { id: 'overview', label: 'Overview', icon: Gauge },
  { id: 'comparison', label: 'Compare', icon: Star }
];

const EarthTesters = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('overview');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tab = searchParams.get('tab');
    if (tab) setActiveTab(tab);
  }, [location.search]);

  // Hero Section
  const HeroSection = () => (
    <section className="relative min-h-[60vh] flex items-center justify-center py-6 md:py-12 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
        <div className="absolute top-20 right-20 w-32 h-32 bg-yellow-300 rounded-full opacity-20"></div>
      </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 flex flex-col items-center justify-center w-full">
        <div className="text-center w-full">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8 flex flex-col items-center justify-center w-full"
          >
            <div className="inline-block bg-yellow-400 px-6 py-3 rounded-full mb-4">
              <span className="text-gray-900 font-bold text-lg" style={{ fontFamily: 'Open Sans, sans-serif' }}>
                KRYKARD Earth Testing Solutions
              </span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-bold text-gray-900 leading-tight mb-4" style={{ fontFamily: 'Open Sans, sans-serif' }}>
              EARTH TESTERS
            </h1>
            <p className="text-base md:text-lg lg:text-lg text-black leading-relaxed font-medium max-w-4xl mx-auto mb-8" style={{ fontFamily: 'Open Sans, sans-serif' }}>
              Professional-grade instruments for accurate ground resistance measurements and comprehensive earth system analysis.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center pt-2">
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base"
                onClick={() => navigate('/contact/sales')}
              >
                <span>Request Demo</span>
                <ArrowRight className="h-5 w-5" />
              </button>
              <button
                className="px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:bg-yellow-50 flex items-center justify-center space-x-3 text-base"
                onClick={() => window.open('/public/T&M April 2025.pdf', '_blank')}
              >
                <span>View Brochure</span>
                <FileText className="h-5 w-5" />
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );

  // Product Card (following multimeter design pattern)
  const ProductCard = ({ product }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white border border-yellow-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 flex flex-col h-full"
      style={{ fontFamily: 'Open Sans, sans-serif' }}
    >
      {/* Model Number Badge */}
      <div className="flex justify-end p-3">
        <span className="bg-yellow-100 text-yellow-800 text-xs font-semibold px-3 py-1 rounded-full">
          {product.modelNumber}
        </span>
      </div>
      {/* Product Image */}
      <div className="flex items-center justify-center h-32 md:h-40 bg-yellow-50">
        <img
          src={product.image}
          alt={product.title}
          className="max-h-full max-w-full object-contain"
          onError={e => {
            e.currentTarget.onerror = null;
            e.currentTarget.src = 'https://via.placeholder.com/200x150/FFD700/000000?text=No+Image';
          }}
        />
      </div>
      <div className="p-4 flex-1 flex flex-col justify-between">
        <div className="text-center mb-2">
          <h3 className="text-base font-bold text-gray-900">{product.title}</h3>
          <div className="text-xs text-gray-600 mt-1">{product.displayInfo}</div>
        </div>
        <button
          onClick={() => navigate(`/measure/earth-testers/product/${product.id}`)}
          className="w-full bg-yellow-300 hover:bg-yellow-400 text-gray-900 font-bold py-2 px-4 rounded-lg transition-all duration-200 mt-4 flex items-center justify-center space-x-2"
        >
          <span>View Details</span>
          <ArrowRight className="inline h-4 w-4 ml-1" />
        </button>
      </div>
    </motion.div>
  );

  // Navigation Component
  const Navigation = () => (
    <nav className="sticky top-0 z-50 bg-white shadow-lg border-b border-gray-200" style={{ fontFamily: 'Open Sans, sans-serif' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="hidden md:flex justify-center py-4">
          <div className="bg-gray-100 p-2 rounded-full flex space-x-2">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-3 font-bold rounded-full transition-all duration-300 flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'bg-yellow-400 text-gray-900 shadow-lg transform -translate-y-0.5'
                    : 'text-gray-600 hover:bg-yellow-50 hover:text-yellow-600'
                }`}
              >
                <tab.icon className="h-5 w-5" />
                <span className="text-base">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
        <div className="md:hidden flex justify-between items-center py-4">
          <span className="font-bold text-gray-900 text-lg">
            {tabs.find(tab => tab.id === activeTab)?.label}
          </span>
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="text-gray-600 hover:text-yellow-600"
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden bg-white border-t border-gray-200"
            >
              <div className="py-4 space-y-2">
                {tabs.map(tab => (
                  <button
                    key={tab.id}
                    onClick={() => {
                      setActiveTab(tab.id);
                      setIsMobileMenuOpen(false);
                    }}
                    className={`w-full text-left px-4 py-3 rounded-lg flex items-center space-x-3 ${
                      activeTab === tab.id
                        ? 'bg-yellow-100 text-yellow-800 font-bold'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <tab.icon className="h-5 w-5" />
                    <span className="text-base">{tab.label}</span>
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </nav>
  );

  // Comparison Table
  const ComparisonTable = () => {
    console.log('EarthTesters ComparisonTable rendering with testers:', earthTesters.length);

    return (
      <div className="comparison-table bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden mt-6 md:mt-8 w-full" style={{ fontFamily: 'Open Sans, sans-serif', display: 'block', visibility: 'visible', opacity: 1 }}>
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 p-4 md:p-6">
          <h3 className="text-xl md:text-2xl font-bold text-center text-gray-900">Model Comparison</h3>
        </div>
        <div className="p-4 md:p-6 overflow-x-auto">
          <table className="min-w-full table-auto border-collapse border border-gray-300">
            <thead>
              <tr className="border-b-2 border-yellow-400">
                <th className="px-4 py-4 bg-yellow-50 text-left font-bold text-gray-900 border border-gray-300">Feature</th>
                {earthTesters.map((tester, idx) => (
                  <th key={idx} className="px-4 py-4 bg-yellow-50 text-center font-bold text-gray-900 min-w-[150px] border border-gray-300">
                    {tester.model}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {[
                { name: 'Poles', values: ['2P/3P', '4P', '3P/4P', '3P/4P'] },
                { name: 'Display', values: ['LCD', 'Digital', 'LCD', 'LCD'] },
                { name: 'Memory', values: ['No', 'No', '512 loc.', '512 rec.'] },
                { name: 'Communication', values: ['No', 'No', 'USB', 'USB'] },
                { name: 'Max Resistance', values: ['50 kΩ', '2,000 Ω', '99.9 kΩ', '99.9 kΩ'] },
                { name: 'Voltage Measurement', values: ['600 V', 'No', 'Yes', 'Yes'] }
              ].map((feature, idx) => (
                <motion.tr
                  key={idx}
                  initial={{ opacity: 0, y: 5 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: idx * 0.05 }}
                  viewport={{ once: true }}
                  className="border-b border-gray-300 hover:bg-yellow-50 transition-colors duration-200"
                >
                  <td className="px-4 py-4 font-semibold text-gray-900 bg-gray-50 border border-gray-300">{feature.name}</td>
                  {feature.values.map((value, i) => (
                    <td key={i} className="px-4 py-4 text-center font-medium text-gray-700 border border-gray-300">
                      {value}
                    </td>
                  ))}
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      {/* Hero Section */}
      <HeroSection />
      {/* Navigation Tabs */}
      <Navigation />
      {/* Tab Content */}
      {activeTab === 'overview' && (
        <section id="products-section" className="py-12 md:py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-10"
            >
              <div className="inline-block bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                PRODUCTS
              </div>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Our Earth Tester Range
              </h2>
              <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-2">
                Choose the perfect earth tester for your ground resistance measurement needs
              </p>
            </motion.div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
              {earthTesters.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Key Features Section */}
      {activeTab === 'overview' && (
        <section className="py-12 md:py-16 bg-gradient-to-br from-yellow-50 to-white">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6 text-center">Key Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white rounded-xl shadow p-6 flex flex-col items-center text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Gauge className="h-7 w-7 text-yellow-500" />
                  <h3 className="font-bold text-lg text-gray-900">High Precision</h3>
                </div>
                <p className="text-gray-700">Advanced technology for precise resistance measurements with industry-leading accuracy up to ±2% of reading.</p>
              </div>
              <div className="bg-white rounded-xl shadow p-6 flex flex-col items-center text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Zap className="h-7 w-7 text-yellow-500" />
                  <h3 className="font-bold text-lg text-gray-900">Multiple Methods</h3>
                </div>
                <p className="text-gray-700">Support for 2-pole, 3-pole, 4-pole, and selective testing techniques for comprehensive earth system analysis.</p>
              </div>
              <div className="bg-white rounded-xl shadow p-6 flex flex-col items-center text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <FileText className="h-7 w-7 text-yellow-500" />
                  <h3 className="font-bold text-lg text-gray-900">Data Management</h3>
                </div>
                <p className="text-gray-700">Built-in memory and connectivity options for storing measurements and transferring data to computers for documentation.</p>
              </div>
            </div>
          </div>
        </section>
      )}

      {activeTab === 'comparison' && (
        <section className="py-12 md:py-16 min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-10"
            >
              <div className="inline-block bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                COMPARISON
              </div>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Compare Our Models
              </h2>
              <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-8">
                Find the perfect earth tester for your specific requirements
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="w-full"
            >
              <ComparisonTable />
            </motion.div>
            {/* Debug info - remove in production */}
            <div className="mt-8 text-center text-sm text-gray-500">
              Active Tab: {activeTab} | Earth Testers: {earthTesters.length}
            </div>
          </div>
        </section>
      )}

      {/* Contact Section */}
      <section className="py-12 md:py-16 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200 mt-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Need Expert Advice?
            </h2>
            <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
              Our specialists provide comprehensive guidance on earth testing solutions
            </p>
            <button
              className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto mt-2"
              onClick={() => navigate('/contact/sales')}
            >
              <span>Contact Sales</span>
              <ArrowRight className="h-5 w-5" />
            </button>
          </motion.div>
        </div>
      </section>

      {/* CSS Override for table borders visibility */}
      <style>{`
        .comparison-table {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }

        .comparison-table table {
          border-collapse: collapse !important;
          border: 2px solid #d1d5db !important;
        }

        .comparison-table table th,
        .comparison-table table td {
          border: 1px solid #d1d5db !important;
          border-collapse: collapse !important;
        }

        .comparison-table table thead tr {
          border-bottom: 3px solid #fbbf24 !important;
        }

        .comparison-table table tbody tr {
          border-bottom: 1px solid #d1d5db !important;
        }
      `}</style>
    </PageLayout>
  );
};

export default EarthTesters;