import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Check,
  Download,
  Mail,
  Phone,
  Shield,
  Gauge,
  FileText,
  ChevronDown,
  Database,
  Zap,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';

// Product Images Showcase Component
const ProductImagesShowcase = ({ product }: { product: any }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const images = product.images || [{ src: product.image, label: product.model }];

  const handlePrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? images.length - 1 : prevIndex - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex === images.length - 1 ? 0 : prevIndex + 1));
  };

  return (
    <div className="rounded-2xl p-6 mb-8 mt-8">
      <div className="relative h-64 sm:h-80 md:h-96 mb-4 bg-gradient-to-br from-yellow-100/30 to-yellow-50/20 rounded-xl flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-center h-full"
          >
            <img
              src={images[currentIndex].src}
              alt={`${product.model} - ${images[currentIndex].label}`}
              className="max-h-full max-w-full object-contain"
            />
          </motion.div>
        </div>
      </div>

      {/* Navigation arrows - positioned below the image */}
      {images.length > 1 && (
        <div className="flex justify-center items-center space-x-4 mb-4">
          <button
            onClick={handlePrev}
            className="bg-yellow-400/80 hover:bg-yellow-400 text-black rounded-full p-2 shadow-md transition-all duration-200 hover:scale-110"
            aria-label="Previous image"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
          <button
            onClick={handleNext}
            className="bg-yellow-400/80 hover:bg-yellow-400 text-black rounded-full p-2 shadow-md transition-all duration-200 hover:scale-110"
            aria-label="Next image"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Image label */}
      <div className="text-center">
        <p className="text-sm text-black font-semibold">
          {images[currentIndex].label}
        </p>
      </div>
    </div>
  );
};

// --- Insulation Testers Data (copy from InsulationTesters.tsx, but as an object keyed by id for fast lookup) ---
const insulationTesters = {
  1: {
    id: 1,
    model: 'CA 6522/CA 6528',
    title: '1kV Insulation Testers',
    subtitle: 'Professional 1kV Insulation Testers',
    image: '/insulation testers/CA 6522.png',
    images: [
      { src: '/insulation testers/CA 6522.png', label: 'CA 6522 1kV Insulation Tester' },
      { src: '/insulation testers/CA 6528.png', label: 'CA 6528 1kV Insulation Tester' }
    ],
    features: [
      'Display : 4000 counts, double digital backlit LCD screen',
      'Logarithmic bargraph (CA 6522)',
      'Continuity at 200mA',
      'CA 6528 : 0.02Ω - 40Ω CA 6522 : 0.00Ω - 10.00Ω',
      'Visual alarm : Blue/Red backlighting (CA 6528)',
      'Timer (mins) : Upto 39:59',
      'Automatic shutdown, Hold,Manual / Lock / Duration modes',
      'IP 40 (CA 6528) , IP 54 (CA 6522)'
    ],
    technicalSpecs: {
      'Test Voltage': '250 V, 500 V, 1000 V',
      'Range/Accuracy': 'CA 6522 : 50 kΩ to 40 GΩ /± (3% +2 counts), CA 6528 : 50 kΩ to 11GΩ / ± (1.5% R+10 pt)',
      'Voltage Range': 'Upto 700 V'
    },
    applications: [
      'Electrical maintenance',
      'Troubleshooting',
      'Industrial field use'
    ],
    advantages: [
      'High accuracy',
      'Visual alarms',
      'IP rated for durability'
    ]
  },
  2: {
    id: 2,
    model: 'CA 6524/CA 6526',
    title: '1kV Insulation Testers',
    subtitle: 'Advanced 1kV Insulation Testers',
    image: '/insulation testers/CA 6524.png',
    images: [
      { src: '/insulation testers/CA 6524.png', label: 'CA 6524 Advanced 1kV Insulation Tester' },
      { src: '/insulation testers/CA 6526.png', label: 'CA 6526 Advanced 1kV Insulation Tester' }
    ],
    features: [
      'Display : 4000 counts, double + bargraph',
      'Continuity at 200mA (0.00Ω - 10.00Ω)/ 20mA (0.0Ω - 100.0Ω)',
      'PI & DAR',
      'Visual Pass/Fail : Red/Green (CA 6526)',
      'Timer (mins) : Upto 39:59',
      'Configurable alarms',
      'Memory : 300 measurements (CA 6524), 1300 measurements (CA 6526)',
      'Communication : Bluetooth (CA 6526)'
    ],
    technicalSpecs: {
      'Test Voltage': '50 V, 100 V, 250 V, 500 V, 1000 V',
      'Test current': '0.01μA to 2mA',
      'Range': '10 kΩ to 200 GΩ',
      'Accuracy': '± (3% +2 counts)',
      'Voltage Range': 'Upto 700 V',
      'Capacitance': '0.1 nF to 10 μF (CA 6526)'
    },
    applications: [
      'Industrial',
      'Field testing',
      'Quality assurance'
    ],
    advantages: [
      'Bluetooth connectivity',
      'Large memory',
      'Configurable alarms'
    ]
  },
  3: {
    id: 3,
    model: 'CA 6532/CA 6534/CA 6536',
    title: 'Insulation Testers Special Models',
    subtitle: 'Specialized Insulation Testers',
    image: '/insulation testers/CA 6532.png',
    images: [
      { src: '/insulation testers/CA 6532.png', label: 'CA 6532 Specialized Insulation Tester' },
      { src: '/insulation testers/CA 6534.png', label: 'CA 6534 Specialized Insulation Tester' },
      { src: '/insulation testers/CA 6536.png', label: 'CA 6536 Specialized Insulation Tester' }
    ],
    features: [
      'Display : 4000 counts, double + bargraph',
      'PI & DAR (CA 6532)',
      'Continuity at 200mA (0.00Ω - 10.00Ω)/ 20mA (0.0Ω - 100.0Ω)',
      'Timer (mins) : Upto 39:59',
      'Memory : 1300 records (CA 6532 & 6534)',
      'Communication : Bluetooth (CA 6532 & 6534)'
    ],
    technicalSpecs: {
      'Test Voltage/Insulation Range': 'CA 6532 : 50 V, 100 V/10 kΩ to 20 GΩ; CA 6534 : 10 V, 25 V, 100 V, 250 V, 500 V/ 2 kΩ to 50 GΩ; CA 6536 : 10 V to 100 V in 1V step/2 kΩ to 20 GΩ',
      'Test current': '0.01μA to 2mA',
      'Accuracy': '± (3% +2 counts)',
      'Voltage Range': 'Upto 700 V',
      'Capacitance': '0.1 nF to 10 nF (CA 6532)'
    },
    applications: [
      'Telecommunications',
      'Electronics',
      'Avionics, Space & Defense'
    ],
    advantages: [
      'Specialized voltage steps',
      'Bluetooth',
      'Large memory'
    ]
  },
  4: {
    id: 4,
    model: 'CA 6505/CA 6545',
    title: '5kV Insulation Testers',
    subtitle: '5kV High Voltage Insulation Testers',
    image: '/insulation testers/CA 6505.png',
    images: [
      { src: '/insulation testers/CA 6505.png', label: 'CA 6505 5kV High Voltage Insulation Tester' },
      { src: '/insulation testers/CA 6545.png', label: 'CA 6545 5kV High Voltage Insulation Tester' }
    ],
    features: [
      'Display : Backlit LCD graphic display with bargraph',
      'Programmable test duration',
      'Automatic calculation of the DAR/PI',
      'DD calculation (CA 6545)',
      'Locking test voltage',
      'Programmable Alarms (CA 6545)',
      'Smoothing of Display (CA 6545)',
      'Automatic detection of the presence of AC or DC external voltage on terminals',
      'Auto power save mode to save battery power'
    ],
    technicalSpecs: {
      'Test voltage (Fixed/Adjustable)': '500 V, 1000 V, 2500 V, 5000 V/40 V to 5100 V in 10 V or 100 V increments',
      'Range': '10 kΩ to 10 TΩ',
      'Accuracy': '± 5% +3 pts',
      'Voltage Range': 'Upto 5100 V',
      'Leakage Current': 'Upto 3mA',
      'Capacitance': 'Upto 49.99 F'
    },
    applications: [
      'High voltage equipment',
      'Power utilities',
      'Industrial plants'
    ],
    advantages: [
      'Programmable alarms',
      'Auto power save',
      'High voltage range'
    ]
  },
  5: {
    id: 5,
    model: 'CA 6547/CA 6549',
    title: '5kV Insulation Testers',
    subtitle: 'Advanced 5kV Insulation Testers',
    image: '/insulation testers/CA 6547.png',
    images: [
      { src: '/insulation testers/CA 6547.png', label: 'CA 6547 Advanced 5kV Insulation Tester' },
      { src: '/insulation testers/CA 6549.png', label: 'CA 6549 Advanced 5kV Insulation Tester' }
    ],
    features: [
      'Large backlit LCD screen, with digital display & bargraph',
      'Automatic calculation of the DAR/PI/DD ratios',
      'Programmable alarms',
      'Displays a error code in an anamoly condition (CA 6549)',
      'Direct plotting resistance over time (R(t)) curves in display (CA 6549)',
      'Calculation of R at Reference Temperature (T°) (CA 6549)',
      'Memory : 128KB storage capacity',
      'Communication : USB (Two-Way)',
      'PC interface'
    ],
    technicalSpecs: {
      'Test Voltage (Fixed/Adjustable)': '500 V, 1000 V, 2500 V, 5000 V/40 V to 5100 V in 10 V or 100 V increments',
      'Range': '10 kΩ to 10 TΩ',
      'Accuracy': '±5% +3 pts',
      'Voltage Range': 'Upto 5100 V',
      'Leakage Current': 'Upto 3mA',
      'Capacitance': 'Upto 49.99 F'
    },
    applications: [
      'Power system analysis',
      'Industrial maintenance',
      'Utility testing'
    ],
    advantages: [
      'USB communication',
      'Advanced memory',
      'Direct plotting of R(t)'
    ]
  },
  6: {
    id: 6,
    model: 'CA 6550/CA 6555',
    title: '10kV/15kV Insulation Testers',
    subtitle: 'Ultra High Voltage Insulation Testers',
    image: '/insulation testers/CA 6555.png',
    images: [
      { src: '/insulation testers/CA 6555.png', label: 'CA 6555 Ultra High Voltage Insulation Tester' },
      { src: '/insulation testers/CA 6555-2.png', label: 'CA 6555 Alternative View' },
      { src: '/insulation testers/CA 6555-3.png', label: 'CA 6555 Detailed View' },
      { src: '/insulation testers/CA 6550.png', label: 'CA 6550 Ultra High Voltage Insulation Tester' }
    ],
    features: [
      'Large graphical LCD display with backlight & bargraph',
      'Calculation of the DAR/PI/DD ratios',
      'Programmable test duration',
      'Timer (mins) : Upto 99:59',
      'Voltage ramp & step with " burn - in", "early break" & "I - limit" modes',
      'Memory : 80,000 points',
      'Communication :USB',
      'PC interface'
    ],
    technicalSpecs: {
      'Test Voltage/Insulation range': 'CA 6550 : 500 V, 1000 V, 2500 V, 5000 V, 10,000 V/10 kΩ to 25 TΩ; CA 6555 : 500 V, 1000 V, 2500 V, 5000 V, 10,000 V, 15,000 V/10 kΩ to 30 TΩ',
      'Accuracy': '±5% +3 pts',
      'Voltage Range': 'Upto 2500 V AC/Upto 4000 V DC',
      'Leakage Current': 'Upto 8mA',
      'Capacitance': 'Upto 19.99 μF'
    },
    applications: [
      'Utility and grid monitoring',
      'Research and development',
      'Critical facility testing'
    ],
    advantages: [
      'Ultra high voltage',
      'Large memory',
      'USB interface'
    ]
  }
};

export { insulationTesters };

const PDF_URL = '/T&M April 2025.pdf';

const InsulationTesterProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const id = Number(productId);
  const product = insulationTesters[id as keyof typeof insulationTesters];
  const productList = Object.values(insulationTesters);

  useEffect(() => {
    if (!product) {
      navigate('/measure/insulation-testers');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Insulation Tester`;
    }
  }, [product, navigate]);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Feature icon logic
  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd') || feature.toLowerCase().includes('color')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage') || feature.toLowerCase().includes('logging')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('earth')) return <Shield className="h-5 w-5" />;
    if (feature.toLowerCase().includes('alarm')) return <Zap className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>
      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div className="text-center mb-4 md:mb-0">
              <h1 className="text-4xl md:text-5xl font-bold text-black mb-2">
                Insulation Testers
              </h1>
              <p className="text-xl text-black font-medium">
                Professional Insulation Testing Solutions
              </p>
            </div>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              <div
                className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block"
                onMouseEnter={() => setDropdownOpen(true)}
                onMouseLeave={() => setDropdownOpen(false)}
              >
                <div className="relative w-full md:w-auto group">
                  <button
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute z-50 mt-2 right-0 w-full md:w-64 bg-white border border-yellow-400 rounded-xl shadow-lg max-h-60 overflow-y-auto transition-all duration-200">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => { setDropdownOpen(false); navigate(`/measure/insulation-testers/product/${prod.id}`); }}
                          className={`w-full text-left px-4 py-3 text-black font-bold hover:bg-yellow-50 transition-colors duration-150 rounded-xl ${prod.id === product.id ? 'bg-yellow-100' : ''}`}
                        >
                          {prod.model}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={() => navigate('/measure/insulation-testers#products-section')}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        {/* Product Hero Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row gap-8 items-center md:items-start">
            <div className="flex-shrink-0 w-full md:w-1/2 flex justify-center">
              <ProductImagesShowcase product={product} />
            </div>
            <div className="flex-1 md:w-1/2">
              <h2 className="text-3xl font-bold text-yellow-600 mb-2">{product.model}</h2>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">{product.subtitle}</h3>
              <p className="text-gray-800 mb-4">{product.title}</p>
              <div className="flex flex-wrap gap-2 mb-4">
                {product.advantages.map((adv, idx) => (
                  <span key={idx} className="bg-yellow-200 text-yellow-800 px-3 py-1 rounded-full text-xs font-semibold">{adv}</span>
                ))}
              </div>
              <a href={PDF_URL} target="_blank" rel="noopener noreferrer" className="inline-flex items-center px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-black font-bold rounded-lg shadow-md transition duration-300">
                <FileText className="h-5 w-5 mr-2" /> View Brochure
              </a>
            </div>
          </div>
        </div>
        {/* Key Features Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Key Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {product.features.map((feature, idx) => (
              <div key={idx} className="flex items-start bg-white rounded-xl shadow p-4 gap-3">
                <FeatureIcon feature={feature} />
                <span className="text-gray-900 font-medium">{feature}</span>
              </div>
            ))}
          </div>
        </div>
        {/* Technical Specs Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Technical Specifications</h3>
          <div className="bg-white rounded-xl shadow p-6">
            <table className="min-w-full">
              <tbody>
                {Object.entries(product.technicalSpecs).map(([key, value], idx) => (
                  <tr key={idx} className="border-b last:border-b-0">
                    <td className="py-2 pr-4 font-semibold text-gray-700 whitespace-nowrap">{key}</td>
                    <td className="py-2 text-gray-900">{value}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        {/* Applications Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Applications</h3>
          <div className="flex flex-wrap gap-3">
            {product.applications.map((app, idx) => (
              <span key={idx} className="bg-yellow-100 text-yellow-800 px-4 py-2 rounded-lg text-sm font-semibold mb-2">{app}</span>
            ))}
          </div>
        </div>
      </div>
      {/* Contact Sales Section */}
      <div className="w-full flex flex-col items-center justify-center mb-0">
        <div className="w-full max-w-2xl bg-white rounded-b-3xl shadow-2xl py-16 flex flex-col items-center justify-center border-t-4 border-yellow-200 relative z-10">
          <h2 className="text-4xl md:text-5xl font-bold text-black text-center mb-4">Need Expert Advice?</h2>
          <p className="text-xl text-gray-700 text-center mb-8 max-w-2xl">Our specialists provide comprehensive guidance on power quality analysis solutions</p>
          <a
            href="mailto:<EMAIL>"
            className="inline-flex items-center px-8 py-4 bg-yellow-400 hover:bg-yellow-500 text-black font-bold rounded-xl shadow-lg transition duration-300 text-lg focus:outline-none focus:ring-2 focus:ring-yellow-300"
            style={{ boxShadow: '0 8px 32px 0 rgba(245, 200, 66, 0.25)' }}
          >
            Contact Sales
            <span className="ml-3"><Mail className="h-6 w-6" /></span>
          </a>
        </div>
        {/* Gradient Divider for separation */}
        <div className="w-full h-10 -mt-2" style={{ background: 'linear-gradient(to bottom, rgba(255,255,255,0.7), rgba(255,255,255,0))' }} />
      </div>
    </PageLayout>
  );
};

export default InsulationTesterProduct;