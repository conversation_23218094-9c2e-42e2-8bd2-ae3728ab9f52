import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Check,
  Download,
  Phone,
  Zap,
  Monitor,
  Database,
  Wifi,
  Gauge,
  ChevronDown,
  BarChart,
  ArrowRight, // Added for ContactSection
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';

// Product Images Showcase Component
const ProductImagesShowcase = ({ product }: { product: any }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const images = product.images || [{ src: product.image, label: product.model }];

  const handlePrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? images.length - 1 : prevIndex - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex === images.length - 1 ? 0 : prevIndex + 1));
  };

  return (
    <div className="rounded-2xl p-6 mb-8 mt-8">
      <div className="relative h-64 sm:h-80 md:h-96 mb-4 bg-gradient-to-br from-yellow-100/30 to-yellow-50/20 rounded-xl flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-center h-full"
          >
            <img
              src={images[currentIndex].src}
              alt={`${product.model} - ${images[currentIndex].label}`}
              className="max-h-full max-w-full object-contain"
            />
          </motion.div>
        </div>
      </div>

      {/* Navigation arrows - positioned below the image */}
      {images.length > 1 && (
        <div className="flex justify-center items-center space-x-4 mb-4">
          <button
            onClick={handlePrev}
            className="bg-yellow-400/80 hover:bg-yellow-400 text-black rounded-full p-2 shadow-md transition-all duration-200 hover:scale-110"
            aria-label="Previous image"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
          <button
            onClick={handleNext}
            className="bg-yellow-400/80 hover:bg-yellow-400 text-black rounded-full p-2 shadow-md transition-all duration-200 hover:scale-110"
            aria-label="Next image"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Image label */}
      <div className="text-center">
        <p className="text-sm text-black font-semibold">
          {images[currentIndex].label}
        </p>
      </div>
    </div>
  );
};

const ClampMeterProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Clamp meter product data
  const productData = {
    power: {
      id: 'power',
      model: 'F205/F404/F604',
      subtitle: 'Power Clamp Meter',
      voltage: 'Up to 1,200V AC/1,700V DC',
      measurement: 'Power & Energy',
      accuracy: '1% + 3 counts',
      price: 'Contact for pricing',
      description: 'The Power Clamp Meter series offers professional-grade, true RMS measurement for both AC and DC, with advanced power analysis and robust safety features for industrial and commercial use.',
      keyFeatures: [
        'True RMS reading on AC and AC+DC',
        'AC and DC voltage up to 1,000 V',
        'Current: up to 2,000 A AC / 3,000 A DC',
        'Measures kW, kVAr, kVA & PF',
        'Auto range, Hold & Auto power off',
        'IP 40 (F205), IP 54 (F404 & F604)'
      ],
      technicalSpecs: {
        'Clamping Diameter': 'F205: 34 mm, F404: 48 mm, F604: 60 mm',
        'Display': 'F205: 6000 counts, F404/F604: 10,000 counts',
        'Temperature': 'Up to 1000°C (F404 & F604)',
        '1 phase & 3 phase power': 'Up to 600 KW (F205)',
        'IP Rating': 'IP 40 (F205), IP 54 (F404 & F604)'
      },
      applications: [
        'Electrical installation testing',
        'Industrial maintenance',
        'Power quality analysis',
        'Panel diagnostics',
        'Energy audits'
      ],
      advantages: [
        'Robust and reliable',
        'Wide measurement range',
        'Advanced power analysis',
        'User-friendly interface',
        'Portable and durable'
      ],
      image: '/clammeter/F205.png',
      images: [
        { src: '/clammeter/F205.png', label: 'F205 Power Clamp Meter' },
        { src: '/clammeter/F404.png', label: 'F404 Power Clamp Meter' },
        { src: '/clammeter/F404 -2.png', label: 'F404 Alternative View' },
        { src: '/clammeter/F604.png', label: 'F604 Power Clamp Meter' }
      ]
    },
    solar: {
      id: 'solar',
      model: 'F406/F606',
      subtitle: 'Solar Clamp Meter',
      voltage: 'Up to 1,200V AC/1,700V DC',
      measurement: 'Power & Energy',
      accuracy: '1% + 3 counts',
      price: 'Contact for pricing',
      description: 'The Solar Clamp Meter series is designed for photovoltaic and renewable energy applications, supporting high DC voltages and advanced power measurements for solar professionals.',
      keyFeatures: [
        'Specially designed for Photo voltaic applications',
        'True RMS reading on AC and AC+DC',
        'AC and DC voltage up to 1,700 V DC',
        'Current: up to 2,000 A AC / 3,000 A DC',
        'Measures kW, kVAr, kVA & PF',
        'PV specific features'
      ],
      technicalSpecs: {
        'Clamping Diameter': 'F406: 48 mm, F606: 60 mm',
        'Display': '10,000 counts',
        '1 phase & 3 phase Power': 'F406: Up to 1,200 kW, F606: Up to 2,400 kW',
        'Voltage & Current THDf/THDr': 'Available',
        'IP Rating': 'IP 54'
      },
      applications: [
        'Solar power system monitoring',
        'Renewable energy troubleshooting',
        'PV installation verification',
        'High-voltage DC measurement',
        'Energy audits'
      ],
      advantages: [
        'High DC voltage support',
        'Optimized for solar',
        'Accurate and reliable',
        'Easy to use',
        'Durable construction'
      ],
      image: '/clammeter/F406.png',
      images: [
        { src: '/clammeter/F406.png', label: 'F406 Solar Clamp Meter' },
        { src: '/clammeter/f406_2_.jpg', label: 'F406 Detailed View' },
        { src: '/clammeter/F606.png', label: 'F606 Solar Clamp Meter' },
        { src: '/clammeter/f606_1_.jpg', label: 'F606 Application View' }
      ]
    },
    harmonics: {
      id: 'harmonics',
      model: 'F407/F607',
      subtitle: 'Power & Harmonics Clamp Meter',
      voltage: 'Up to 1,000V',
      measurement: 'Power & Harmonics',
      accuracy: '1% + 3 counts',
      price: 'Contact for pricing',
      description: 'The Power & Harmonics Clamp Meter series provides advanced harmonic analysis, Bluetooth communication, and data logging for in-depth power quality troubleshooting.',
      keyFeatures: [
        'True RMS reading on AC and AC+DC',
        'Harmonics up to 25th order',
        'Bluetooth Communication',
        'Data recording & PC interface',
        'Measures kW, kVAr, kVA, PF & DPF',
        'Auto range, Hold & Auto power off'
      ],
      technicalSpecs: {
        'Clamping Diameter': 'F407: 48 mm, F607: 60 mm',
        'Display': '10,000 counts',
        'Harmonic Analysis': 'Voltage & Current THDf/THDr, Individual Harmonics up to 25° order',
        '1 phase & 3 phase Power': 'F407: Up to 1,000 kW, F607: Up to 2,000 kW',
        'Bluetooth': 'Yes',
        'IP Rating': 'IP 54'
      },
      applications: [
        'Power quality analysis',
        'Industrial equipment monitoring',
        'Harmonic troubleshooting',
        'Data logging',
        'Panel diagnostics'
      ],
      advantages: [
        'Advanced harmonic analysis',
        'Bluetooth connectivity',
        'Comprehensive measurement',
        'Easy data export',
        'Professional build quality'
      ],
      image: '/clammeter/F407.png',
      images: [
        { src: '/clammeter/F407.png', label: 'F407 Power & Harmonics Clamp Meter' },
        { src: '/clammeter/f407_f962_1.jpg', label: 'F407 with Accessories' },
        { src: '/clammeter/F607.png', label: 'F607 Power & Harmonics Clamp Meter' }
      ]
    }
  };

  const product = productData[productId as keyof typeof productData];
  const productList = Object.values(productData);

  useEffect(() => {
    if (!product) {
      navigate('/measure/clampmeters');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Clamp Meter`;
    }
  }, [product, navigate]);

  useEffect(() => {
    if (!dropdownOpen) return;
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [dropdownOpen]);

  if (!product) {
    return <div>Product not found</div>;
  }

  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd') || feature.toLowerCase().includes('color')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage') || feature.toLowerCase().includes('logging')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('communication') || feature.toLowerCase().includes('bluetooth') || feature.toLowerCase().includes('pc')) return <Wifi className="h-5 w-5" />;
    if (feature.toLowerCase().includes('power')) return <Gauge className="h-5 w-5" />;
    if (feature.toLowerCase().includes('harmonic')) return <BarChart className="h-5 w-5" />;
    if (feature.toLowerCase().includes('alarm')) return <Zap className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  // Add a handler for back to products
  const handleBackToProducts = () => {
    navigate('/measure/clamp-meters');
    setTimeout(() => {
      const section = document.querySelector('div.inline-block.bg-yellow-100');
      if (section) section.scrollIntoView({ behavior: 'smooth' });
    }, 300);
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>
      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div className="text-center mb-4 md:mb-0">
              <h1 className="typography-h1 text-black mb-2">
                Clamp Meters
              </h1>
              <p className="typography-h4 text-black">
                Professional Clamp Meter Solutions
              </p>
            </div>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              {/* Refactored dropdown logic here */}
              <div
                className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block"
                ref={dropdownRef}
              >
                <div className="relative w-full md:w-auto group">
                  <button
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                    onClick={() => setDropdownOpen((open) => !open)}
                    aria-haspopup="true"
                    aria-expanded={dropdownOpen}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute z-50 mt-2 right-0 w-full md:w-64 bg-white border border-yellow-400 rounded-xl shadow-lg max-h-60 overflow-y-auto transition-all duration-200">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => { setDropdownOpen(false); navigate(`/measure/clampmeters/product/${prod.id}`); }}
                          className={`w-full text-left px-4 py-3 text-black font-bold hover:bg-yellow-50 transition-colors duration-150 rounded-xl ${prod.id === product.id ? 'bg-yellow-100' : ''}`}
                          style={{ fontWeight: 700, fontSize: '1.1rem' }}
                        >
                          {prod.model}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={handleBackToProducts}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-8 md:py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center md:items-stretch gap-6 md:gap-8">
              <div className="w-full md:w-1/2 max-w-2xl order-2 md:order-1 flex flex-col justify-center">
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="space-y-4 order-1 lg:order-1"
                >
                  <div className="inline-block px-3 py-1.5 rounded-full text-black font-bold text-xs mb-3" style={{ backgroundColor: '#F5C842' }}>
                    {product.voltage} Voltage Range
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-black mb-3">
                    {product.model}
                  </h1>
                  <p className="text-lg text-yellow-700 font-semibold mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-base text-black leading-relaxed mb-6">
                    {product.description}
                  </p>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Measurement</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.measurement}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Accuracy</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.accuracy}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                      <h4 className="font-semibold text-black mb-1">Price</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.price}</p>
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button onClick={() => navigate('/contact/sales')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Phone className="h-5 w-5" />
                      <span>Request Demo</span>
                    </button>
                    <button onClick={() => window.open('/T&M April 2025.pdf', '_blank')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Download className="h-5 w-5" />
                      <span>View Brochure</span>
                    </button>
                  </div>
                </motion.div>
              </div>
              <div className="w-full md:w-1/2 max-w-lg flex items-center justify-center order-1 md:order-2 mb-6 md:mb-0">
                <ProductImagesShowcase product={product} />
              </div>
            </div>
          </div>
        </div>
        {/* Content Sections */}
        <div id="content-section" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-12">
          <div className="flex flex-col md:flex-row gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="w-full md:w-1/2 bg-white rounded-2xl shadow-lg p-8 mb-8 md:mb-0"
            >
              <h2 className="text-3xl font-bold text-black mb-8">Key Features</h2>
              <div className="grid grid-cols-1 gap-6">
                {product.keyFeatures.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <FeatureIcon feature={feature} />
                    <span className="text-lg text-gray-800 font-">{feature}</span>
                  </div>
                ))}
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="w-full md:w-1/2 bg-white rounded-2xl shadow-lg p-8"
            >
              <h2 className="text-3xl font-bold text-black mb-8">Technical Specifications</h2>
              <div className="grid grid-cols-1 gap-4">
                {Object.entries(product.technicalSpecs).map(([spec, value], idx) => (
                  <div key={idx} className="flex items-start gap-3">
                    <span className="font-semibold text-black min-w-[160px]">{spec}:</span>
                    <span className="text-gray-800">{value}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
          <div className="flex flex-col md:flex-row gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="w-full md:w-1/2 bg-white rounded-2xl shadow-lg p-8 mb-8 md:mb-0"
            >
              <h2 className="text-3xl font-bold text-black mb-8">Applications</h2>
              <ul className="list-disc pl-6 space-y-2">
                {product.applications.map((app, idx) => (
                  <li key={idx} className="text-lg text-gray-800 ">{app}</li>
                ))}
              </ul>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="w-full md:w-1/2 bg-white rounded-2xl shadow-lg p-8"
            >
              <h2 className="text-3xl font-bold text-black mb-8">Advantages</h2>
              <ul className="list-disc pl-6 space-y-2">
                {product.advantages.map((adv, idx) => (
                  <li key={idx} className="text-lg text-gray-800 font-xs">{adv}</li>
                ))}
              </ul>
            </motion.div>
          </div>
        </div>
        {/* Contact Section at the end */}
        <section className="py-12 md:py-16 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200 mt-10">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Need Expert Advice?
              </h2>
              <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
                Our specialists provide comprehensive guidance on clamp meter solutions
              </p>
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto mt-2"
                onClick={() => navigate('/contact/sales')}
              >
                <span>Contact Sales</span>
                <ArrowRight className="h-5 w-5" />
              </button>
            </motion.div>
          </div>
        </section>
      </div>
    </PageLayout>
  );
};

export default ClampMeterProduct; 