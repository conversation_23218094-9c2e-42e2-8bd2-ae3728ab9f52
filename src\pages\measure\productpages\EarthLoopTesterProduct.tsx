import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Check,
  Download,
  Mail,
  Shield,
  Gauge,
  FileText,
  ChevronDown,
  Phone,
  Database,
  Monitor,
  Zap,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';

// Product Images Showcase Component
const ProductImagesShowcase = ({ product }: { product: any }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const images = product.images || [{ src: product.image, label: product.model }];

  const handlePrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? images.length - 1 : prevIndex - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex === images.length - 1 ? 0 : prevIndex + 1));
  };

  return (
    <div className="rounded-2xl p-6 mb-8 mt-8">
      <div className="relative h-64 sm:h-80 md:h-96 mb-4 bg-gradient-to-br from-yellow-100/30 to-yellow-50/20 rounded-xl flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-center h-full"
          >
            <img
              src={images[currentIndex].src}
              alt={`${product.model} - ${images[currentIndex].label}`}
              className="max-h-full max-w-full object-contain"
            />
          </motion.div>
        </div>
      </div>

      {/* Navigation arrows - positioned below the image */}
      {images.length > 1 && (
        <div className="flex justify-center items-center space-x-4 mb-4">
          <button
            onClick={handlePrev}
            className="bg-yellow-400/80 hover:bg-yellow-400 text-black rounded-full p-2 shadow-md transition-all duration-200 hover:scale-110"
            aria-label="Previous image"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
          <button
            onClick={handleNext}
            className="bg-yellow-400/80 hover:bg-yellow-400 text-black rounded-full p-2 shadow-md transition-all duration-200 hover:scale-110"
            aria-label="Next image"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Image label */}
      <div className="text-center">
        <p className="text-sm text-black font-semibold">
          {images[currentIndex].label}
        </p>
      </div>
    </div>
  );
};

const earthLoopTesterData = {
  'CA-6417': {
    id: 'CA-6417',
    model: 'CA 6417',
    subtitle: 'Clamp-On Earth Loop Tester',
    image: '/earth_loop_testers/CA 6417.png',
    images: [
      { src: '/earth_loop_testers/CA 6417.png', label: 'CA 6417 Clamp-On Earth Loop Tester' },
      { src: '/earth_loop_testers/CA 6418.png', label: 'CA 6418 Earth Loop Tester' },
      { src: '/earth_loop_testers/images-removebg-preview (1).png', label: 'Earth Loop Tester Application' }
    ],
    display: 'Clamp-on, LCD display',
    description: 'Non-invasive clamp-on tester for fast, safe earth resistance measurement without disconnecting the ground system.',
    keyFeatures: [
      'Clamp-on measurement for non-invasive testing',
      'Automatic self-calibration',
      'Large LCD display',
      'Measures earth resistance and leakage current',
      'Memory for up to 300 measurements',
      'Alarm function for threshold monitoring',
      'Robust, portable design'
    ],
    technicalSpecs: {
      'Measurement Range': '0.01Ω to 1200Ω',
      'Current Measurement': '0.2mA to 40A',
      'Display': 'LCD, 4000 counts',
      'Jaw Opening': '35mm',
      'Memory': '300 measurements',
      'Power Supply': '4 x 1.5V AA',
      'Operating Temperature': '-10°C to +55°C',
      'Weight': '1.1kg'
    },
    applications: [
      'Ground system verification',
      'Industrial plant maintenance',
      'Lightning protection system testing',
      'Utility and substation testing'
    ]
  },
  'CA-6418': {
    id: 'CA-6418',
    model: 'CA 6418',
    subtitle: 'Advanced Clamp-On Earth Tester',
    image: '/earth_loop_testers/CA 6418.png',
    display: 'Clamp-on, LCD display',
    description: 'Advanced clamp-on earth tester with Bluetooth communication and extended measurement range.',
    keyFeatures: [
      'Bluetooth communication for data transfer',
      'Extended measurement range',
      'Automatic self-calibration',
      'Large LCD display',
      'Measures earth resistance and leakage current',
      'Alarm and memory functions',
      'Rugged, ergonomic design'
    ],
    technicalSpecs: {
      'Measurement Range': '0.01Ω to 2000Ω',
      'Current Measurement': '0.2mA to 40A',
      'Display': 'LCD, 4000 counts',
      'Jaw Opening': '35mm',
      'Memory': '2000 measurements',
      'Bluetooth': 'Yes',
      'Power Supply': '4 x 1.5V AA',
      'Operating Temperature': '-10°C to +55°C',
      'Weight': '1.2kg'
    },
    applications: [
      'Ground system verification',
      'Industrial plant maintenance',
      'Lightning protection system testing',
      'Utility and substation testing'
    ]
  }
};

const EarthLoopTesterProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const product = earthLoopTesterData[productId as keyof typeof earthLoopTesterData];
  const productList = Object.values(earthLoopTesterData);

  useEffect(() => {
    if (!product) {
      navigate('/measure/earth-loop-testers');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Earth Loop Tester`;
    }
  }, [product, navigate]);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Feature icon logic similar to analyzer
  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('bluetooth')) return <Shield className="h-5 w-5" />;
    if (feature.toLowerCase().includes('alarm')) return <Zap className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            {/* Title always at top in mobile, center in desktop */}
            <div className="text-center mb-4 md:mb-0">
              <h1 className="text-4xl md:text-5xl font-bold text-black mb-2">
                Earth Loop Testers
              </h1>
              <p className="text-xl text-black font-medium">
                Professional Earth Loop Testing Solutions
              </p>
            </div>
            {/* Responsive flex container for dropdown and back button */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              {/* Dropdown first on mobile, right on desktop */}
              <div
                className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block"
                onMouseEnter={() => setDropdownOpen(true)}
                onMouseLeave={() => setDropdownOpen(false)}
              >
                <div className="relative w-full md:w-auto group">
                  <button
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute z-50 mt-2 right-0 w-full md:w-64 bg-white border border-yellow-400 rounded-xl shadow-lg max-h-60 overflow-y-auto transition-all duration-200">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => { setDropdownOpen(false); navigate(`/measure/earth-loop-testers/product/${prod.id}`); }}
                          className={`w-full text-left px-4 py-3 text-black font-bold hover:bg-yellow-50 transition-colors duration-150 rounded-xl ${prod.id === product.id ? 'bg-yellow-100' : ''}`}
                          style={{ fontWeight: 700, fontSize: '1.1rem' }}
                        >
                          {prod.model}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Back button second on mobile, left on desktop */}
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={() => navigate('/measure/earth-loop-testers#products-section')}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-8 md:py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center md:items-stretch gap-6 md:gap-8">
              {/* Content Left (on desktop) */}
              <div className="w-full md:w-1/2 max-w-2xl order-2 md:order-1 flex flex-col justify-center">
                {/* Specs Section */}
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="space-y-4 order-1 lg:order-1"
                >
                  <div className="inline-block px-3 py-1.5 rounded-full text-black font-bold text-xs mb-3" style={{ backgroundColor: '#F5C842' }}>
                    {product.display}
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-black mb-3">
                    {product.model}
                  </h1>
                  <p className="text-lg text-yellow-700 font-semibold mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-base text-black leading-relaxed mb-6">
                    {product.description}
                  </p>
                  {/* Quick Specs - Only show if present */}
                  <div className="grid grid-cols-2 gap-3">
                    {product.technicalSpecs['Measurement Range'] && (
                      <div className="bg-white p-3 rounded-xl shadow-md">
                        <h4 className="font-semibold text-black mb-1">Measurement Range</h4>
                        <p className="font-bold" style={{ color: '#B8860B' }}>{product.technicalSpecs['Measurement Range']}</p>
                      </div>
                    )}
                    {product.technicalSpecs['Current Measurement'] && (
                      <div className="bg-white p-3 rounded-xl shadow-md">
                        <h4 className="font-semibold text-black mb-1">Current Measurement</h4>
                        <p className="font-bold" style={{ color: '#B8860B' }}>{product.technicalSpecs['Current Measurement']}</p>
                      </div>
                    )}
                    {product.technicalSpecs['Bluetooth'] && (
                      <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                        <h4 className="font-semibold text-black mb-1">Bluetooth</h4>
                        <p className="font-bold" style={{ color: '#B8860B' }}>{product.technicalSpecs['Bluetooth']}</p>
                      </div>
                    )}
                  </div>
                  {/* Action Buttons at Bottom */}
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button onClick={() => navigate('/contact/sales')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Phone className="h-5 w-5" />
                      <span>Request Demo</span>
                    </button>
                    <button onClick={() => window.open('/public/T&M April 2025.pdf', '_blank')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Download className="h-5 w-5" />
                      <span>View Brochure</span>
                    </button>
                  </div>
                </motion.div>
              </div>
              {/* Image Right (on desktop) */}
              <div className="w-full md:w-1/2 max-w-lg flex items-center justify-center order-1 md:order-2 mb-6 md:mb-0">
                <ProductImagesShowcase product={product} />
              </div>
            </div>
          </div>
        </div>

        {/* Content Sections */}
        <div id="content-section" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-12">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Key Features Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="w-full md:w-1/2 bg-white rounded-2xl shadow-lg p-8 mb-8 md:mb-0"
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Key Features</h2>
              <div className="grid grid-cols-1 gap-6">
                {product.keyFeatures.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    className="flex items-center space-x-4 p-4 rounded-xl hover:bg-yellow-50 transition-colors duration-200"
                  >
                    <div className="flex-shrink-0 w-10 h-10 min-w-[40px] rounded-lg flex items-center justify-center" style={{ backgroundColor: '#F5C842' }}>
                      <FeatureIcon feature={feature} />
                    </div>
                    <p className="flex-1 text-black font-medium leading-relaxed text-xl sm:text-xl lg:text-xl font-black mb-1 font-['Open_Sans']">{feature}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
            {/* Technical Specifications Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="w-full md:w-1/2 bg-white rounded-2xl shadow-lg overflow-hidden p-8"
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Technical Specifications</h2>
              {/* Table Style Layout */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <tbody>
                    {Object.entries(product.technicalSpecs).map(([key, value], index) => (
                      <motion.tr
                        key={key}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.05 }}
                        className={`border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200 ${index % 2 === 0 ? 'bg-gray-25' : 'bg-white'}`}
                      >
                        <td className="py-4 px-8 font-semibold text-gray-900 w-1/3 border-r border-gray-100">
                          <div className="flex items-center space-x-3">
                            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: '#F5C842' }}></div>
                            <span>{key}</span>
                          </div>
                        </td>
                        <td className="py-4 px-8 text-black font-medium">
                          {String(value)}
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </motion.div>
          </div>
          {/* Applications Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-white rounded-2xl shadow-lg p-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Applications</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {product.applications.map((application, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="rounded-xl p-6 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
                  style={{ background: 'linear-gradient(to bottom right, #FEF9E7, #FDF2D0)' }}
                >
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-8 h-8 min-w-[32px] rounded-lg flex items-center justify-center" style={{ backgroundColor: '#F5C842' }}>
                      <Shield className="h-4 w-4 text-gray-900" />
                    </div>
                    <h4 className="flex-1 font-semibold text-gray-900 text-xl sm:text-xl lg:text-xl font-black mb-1 font-['Open_Sans']">{application}</h4>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Contact Section */}
        <div className="py-16" style={{ background: 'linear-gradient(to bottom right, #FEF9E7, #FFFBEA)' }}>
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-black mb-6">
                Ready to Experience Precision?
              </h2>
              <p className="text-xl text-black mb-8 font-medium">
                Contact our experts for personalized recommendations and demonstrations
              </p>
              <div className="flex justify-center">
                <button onClick={() => navigate('/contact/sales')} className="px-8 py-4 text-black font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                  <Mail className="h-5 w-5" />
                  <span>Contact Sales</span>
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default EarthLoopTesterProduct; 