import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  ArrowRight,
  Gauge,
  Zap,
  Shield,
  FileText,
  Menu,
  Star
} from "lucide-react";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";

const PDF_URL = "/T&M April 2025.pdf";

// Product Overview Card Component (matching powerquality.tsx design)
const ProductCard = ({
  title,
  modelNumber,
  image,
  displayInfo,
  onViewDetailsClick
}: {
  title: string;
  modelNumber: string;
  image: string;
  displayInfo: string;
  onViewDetailsClick: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white border border-yellow-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 flex flex-col h-full"
      style={{ fontFamily: 'Open Sans, sans-serif' }}
    >
      {/* Model Number Badge */}
      <div className="flex justify-end p-3">
        <span className="bg-yellow-100 text-black text-xs font-semibold px-3 py-1 rounded-full">
          {modelNumber}
        </span>
      </div>
      {/* Product Image */}
      <div className="flex items-center justify-center h-32 md:h-40 bg-yellow-50">
        <img
          src={image}
          alt={title}
          className="max-h-full max-w-full object-contain"
          onError={e => {
            e.currentTarget.onerror = null;
            e.currentTarget.src = 'https://via.placeholder.com/200x150/FFD700/000000?text=No+Image';
          }}
        />
      </div>
      <div className="p-4 flex-1 flex flex-col justify-between">
        <div className="text-center mb-2">
          <h3 className="text-base font-bold text-gray-900">{title}</h3>
          <div className="text-xs text-gray-600 mt-1">{displayInfo}</div>
        </div>
        <button
          onClick={onViewDetailsClick}
          className="w-full bg-yellow-300 hover:bg-yellow-400 text-gray-900 font-bold py-2 px-4 rounded-lg transition-all duration-200 mt-4 flex items-center justify-center space-x-2"
        >
          <span>View Details</span>
          <ArrowRight className="inline h-4 w-4 ml-1" />
        </button>
      </div>
    </motion.div>
  );
};

// Hero Section (matching powerquality.tsx design)
const HeroSection = ({ onRequestDemo, onViewBrochure }) => (
  <div className="relative py-8 md:py-12 overflow-hidden font-['Open_Sans']">
    {/* Hero Background Elements */}
    <div className="absolute inset-0 z-0">
      <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
      <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
    </div>
    <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div className="grid grid-cols-1 lg:grid-cols-[10%_90%] gap-0 items-center">
        {/* Left Spacer for 10% on large screens */}
        <div className="hidden lg:block"></div>
        {/* Content and Image Side by Side */}
        <div className="lg:flex lg:flex-row lg:items-center lg:gap-4">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-4 text-center lg:text-left lg:w-1/2"
          >
            <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
              <span className="text-sm md:text-base font-semibold text-gray-900 font-['Open_Sans']">KRYKARD Oscilloscope Solutions</span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-bold text-gray-900 leading-tight font-['Open_Sans']">
              OSCILLOSCOPES
            </h1>
            <p className="text-base md:text-lg lg:text-xl text-gray-900 leading-relaxed font-medium text-justify lg:text-left font-['Open_Sans']">
              Professional-grade instruments for precision measurement of electrical signals with multiple operating modes and advanced analysis capabilities.
            </p>
            <div className="pt-2 flex flex-wrap gap-3 justify-center lg:justify-start">
              <Button
                className="px-4 py-2 md:px-6 md:py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2 font-['Open_Sans']"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Button>
              <Button
                className="px-4 py-2 md:px-6 md:py-3 bg-white border-2 border-yellow-400 text-gray-900 font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-yellow-50 flex items-center space-x-2 font-['Open_Sans']"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </div>
          </motion.div>
          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex justify-center lg:justify-center lg:w-1/2 lg:pl-8"
          >
            <div className="relative">
              <img
                src="/oscillosacopes old/OX 5042 , OX 5022.png"
                alt="Oscilloscope Hero"
                className="w-full max-w-md h-auto object-contain"
                onError={e => {
                  e.currentTarget.onerror = null;
                  e.currentTarget.src = 'https://via.placeholder.com/400x300/FFD700/000000?text=Oscilloscope';
                }}
              />
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  </div>
);

// Main Oscilloscopes Component
const Oscilloscopes = () => {
  const navigate = useNavigate();

  // Handler for View Details button - Navigate to individual product pages
  const handleViewDetails = (productType: string) => {
    // Map product types to product IDs for individual pages
    const productIdMap: { [key: string]: string } = {
      'handheld': 'handheld',
      'portable': 'portable'
    };
    const productId = productIdMap[productType];
    if (productId) {
      navigate(`/measure/oscilloscopes/product/${productId}`);
    }
  };

  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };

  // Product Data
  const products = [
    {
      id: "handheld",
      title: "Handheld Oscilloscope Series",
      modelNumber: "OX 5022/OX 5042",
      image: "/oscillosacopes old/OX 5042 , OX 5022.png",
      displayInfo: "20-40 MHz, 2 Isolated Channels"
    },
    {
      id: "portable",
      title: "Portable Oscilloscope Series",
      modelNumber: "OX 9062/OX 9102/OX 9104/OX 9304",
      image: "/oscilloscpoes ox 9104,9304/9J9A3371.JPG",
      displayInfo: "60-300 MHz, 2-4 Isolated Channels"
    }
  ];

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>
      <div className="min-h-screen bg-yellow-50 font-['Open_Sans']">
        {/* Hero Section */}
        <HeroSection onRequestDemo={handleRequestDemo} onViewBrochure={handleViewBrochure} />

        {/* Product Cards Section */}
        <section className="py-12 md:py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-10"
            >
              <div className="inline-block bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                PROFESSIONAL SERIES
              </div>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Our Oscilloscope Range
              </h2>
              <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-2">
                Professional-grade instruments for precision measurement of electrical signals with multiple operating modes
              </p>
            </motion.div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-8">
              {products.map((product) => (
                <ProductCard
                  key={product.id}
                  title={product.title}
                  modelNumber={product.modelNumber}
                  image={product.image}
                  displayInfo={product.displayInfo}
                  onViewDetailsClick={() => handleViewDetails(product.id)}
                />
              ))}
            </div>
          </div>
        </section>
        {/* Key Features Section */}
        <KeyFeaturesSection />
        {/* Contact Section */}
        <ContactSection onContactClick={() => navigate('/contact/sales')} />
      </div>
    </PageLayout>
  );
};

// Feature Highlight Component (matching powerquality.tsx design)
const FeatureHighlight = ({ icon: Icon, title, description }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6 }}
    viewport={{ once: true }}
    className="rounded-2xl border border-yellow-200 hover:border-yellow-400 transition-all duration-300 p-4 h-full bg-transparent flex flex-col items-center text-center"
    style={{ fontFamily: 'Open Sans, sans-serif' }}
  >
    <div className="flex flex-row items-center gap-3 mb-2 justify-center w-full">
      <div className="bg-gradient-to-br from-yellow-400 to-yellow-500 w-10 h-10 rounded-2xl flex items-center justify-center">
        <Icon className="h-6 w-6 text-gray-900" />
      </div>
      <h3 className="text-base md:text-lg font-bold text-gray-900 m-0 p-0">{title}</h3>
    </div>
    <p className="text-gray-700 font-medium text-sm md:text-base leading-relaxed">{description}</p>
  </motion.div>
);

// Key Features Section (matching powerquality.tsx design)
const KeyFeaturesSection = () => {
  return (
    <section className="py-8 md:py-12 bg-yellow-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-8 lg:px-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-14"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-black mb-4 tracking-tight">
            Key Features
          </h2>
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto font-medium mb-2">
            Discover the standout features that make our oscilloscopes the preferred choice for professionals.
          </p>
        </motion.div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 justify-center">
          <FeatureHighlight
            icon={Gauge}
            title="Multi-Mode Capability"
            description="Each device functions as an oscilloscope, multimeter, and harmonic analyzer, providing versatile measurement options in a single instrument."
          />
          <FeatureHighlight
            icon={Zap}
            title="Isolated Channels"
            description="Fully isolated input channels ensure accurate measurements and user safety when working with different circuit potentials."
          />
          <FeatureHighlight
            icon={Shield}
            title="High-Resolution Display"
            description="Crisp, vibrant displays with intuitive interfaces make complex measurements easier to visualize and interpret."
          />
          <FeatureHighlight
            icon={FileText}
            title="Data Logging & Storage"
            description="Extensive memory for long-term data logging and easy export for analysis."
          />
          <FeatureHighlight
            icon={Menu}
            title="User-Friendly Interface"
            description="Intuitive color displays and navigation for quick setup and operation."
          />
          <FeatureHighlight
            icon={Star}
            title="Flexible Connectivity"
            description="USB, Bluetooth, and Ethernet options for seamless data transfer and remote monitoring."
          />
        </div>
      </div>
    </section>
  );
};

// Contact Section Component (matching powerquality.tsx design)
const ContactSection = ({ onContactClick }: { onContactClick: () => void }) => {
  return (
    <section className="py-6 md:py-8 mb-16 md:mb-24 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200 mt-6">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="pb-4"
        >
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
            Need More Information?
          </h2>
          <p className="text-base md:text-lg text-gray-800 mb-6 font-medium max-w-xl mx-auto">
            Our team of experts is ready to help you with product specifications, custom solutions, and pricing.
          </p>
          <button
            className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 flex items-center justify-center space-x-2 text-base mx-auto"
            onClick={onContactClick}
          >
            <span>Contact Our Experts</span>
            <ArrowRight className="h-4 w-4" />
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default Oscilloscopes;
