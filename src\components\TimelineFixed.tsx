
import { useInView } from 'react-intersection-observer';
import { motion } from 'framer-motion';
import React, { useRef, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import * as THREE from 'three';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { MTLLoader } from 'three/examples/jsm/loaders/MTLLoader';

// 3D Stabilizer Model Component
interface StabilizerModelProps {
  modelPath: string;
  texturePath: string;
  mtlPath: string;
}

const StabilizerModel: React.FC<StabilizerModelProps> = ({ modelPath, texturePath, mtlPath }) => {
  const groupRef = useRef<THREE.Group>(null);
  const [obj, setObj] = React.useState<THREE.Object3D | null>(null);

  useEffect(() => {
    // Load the MTL file first
    const mtlLoader = new MTLLoader();
    mtlLoader.load(mtlPath, (materials) => {
      materials.preload();

      // Then load the OBJ file with the materials
      const objLoader = new OBJLoader();
      objLoader.setMaterials(materials);
      objLoader.load(modelPath, (object) => {
        // Apply texture to the model if texturePath is provided
        if (texturePath) {
          const textureLoader = new THREE.TextureLoader();
          const texture = textureLoader.load(texturePath);

          object.traverse((child) => {
            if (child instanceof THREE.Mesh) {
              child.material.map = texture;
              child.material.needsUpdate = true;
            }
          });
        }

        // Center the model
        const box = new THREE.Box3().setFromObject(object);
        const center = box.getCenter(new THREE.Vector3());
        object.position.sub(center);

        // Scale the model to fit the view
        const size = box.getSize(new THREE.Vector3());
        const maxDim = Math.max(size.x, size.y, size.z);
        const scale = 2 / maxDim;
        object.scale.multiplyScalar(scale);

        setObj(object);
      });
    });
  }, [modelPath, texturePath, mtlPath]);

  // Rotate the model
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y += 0.005;
    }
  });

  return (
    <group ref={groupRef}>
      {obj && <primitive object={obj} />}
    </group>
  );
};

// 3D Stabilizer Scene Component
const Stabilizer3DScene: React.FC<{ className?: string }> = ({ className }) => {
  const modelPath = '/3d_stabilizer/tripo_convert_5228f027-7461-432a-a532-24aad1ade9ec.obj';
  const texturePath = '/3d_stabilizer/tripo_image_5228f027-7461-432a-a532-24aad1ade9ec_0.webp';
  const mtlPath = '/3d_stabilizer/tripo_convert_5228f027-7461-432a-a532-24aad1ade9ec.mtl';

  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        shadows
        gl={{ preserveDrawingBuffer: true }}
      >
        <ambientLight intensity={0.5} />
        <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} intensity={1} castShadow />
        <pointLight position={[-10, -10, -10]} intensity={0.5} />
        <StabilizerModel modelPath={modelPath} texturePath={texturePath} mtlPath={mtlPath} />
        <OrbitControls enableZoom={true} enablePan={true} />
        <Environment preset="city" />
      </Canvas>
    </div>
  );
};

const TimelineFixed = () => {
  const { ref, inView } = useInView({ threshold: 0.2, triggerOnce: true });

  return (
    <section id="timeline" className="relative py-16 sm:py-20 lg:py-24 overflow-hidden font-['Open_Sans'] bg-white">
      {/* Removed Background Image and Overlay */}

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Content Section - Left Side */}
          <motion.div
            ref={ref}
            className={`transition-all duration-1000 text-left ${
              inView ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'
            }`}
            initial={{ opacity: 0, x: -100 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {/* Enhanced Header */}
            <div className="mb-6 sm:mb-8">
              <motion.span
                className="inline-block px-4 py-2 bg-gray-100 border border-gray-300 rounded-full text-lg sm:text-xl font-bold text-gray-800 uppercase tracking-wider font-['Open_Sans'] mb-4"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={inView ? { opacity: 1, scale: 1 } : {}}
                transition={{ delay: 0.2, duration: 0.6 }}
              >
                Our Story
              </motion.span>
              <motion.h2
                className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 font-['Open_Sans'] leading-tight"
                initial={{ opacity: 0, y: 30 }}
                animate={inView ? { opacity: 1, y: 0 } : {}}
                transition={{ delay: 0.4, duration: 0.8 }}
              >
                40+ Years of Power Excellence
              </motion.h2>
            </div>

            {/* Enhanced Content */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.6, duration: 0.8 }}
            >
              <p className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify">
                We offer solutions to industrial & commercial establishments under our popular brand KRYKARD. With over <span className="font-bold text-gray-900">5,00,000 installations</span> of Power Conditioners & over <span className="font-bold text-gray-700">1,50,000 installations</span> of Portable & Panel Load Managers, KRYKARD is one of the leading brands in Power Conditioning & Energy Management.
              </p>
              <p className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify">
                State-of-the-art facilities empower us to address the requirements of Indian industries comprehensively, effectively & efficiently, ensuring they derive maximum benefits from the power conditioning & energy management solutions we provide.
              </p>
              <p className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify">
                With a taskforce of around <span className="font-bold text-gray-900">500+ employees</span> & an extensive network of sales & service branches nationwide, we are well-equipped to seamlessly reach out to our customers & fulfil their needs.
              </p>
            </motion.div>
          </motion.div>

          {/* 3D Model Section - Right Side */}
          <motion.div
            className={`transition-all duration-1000 ${
              inView ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'
            }`}
            initial={{ opacity: 0, x: 100 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
          >
            <div className="h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-gray-50 to-gray-100">
              <Stabilizer3DScene className="w-full h-full" />
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default TimelineFixed;
