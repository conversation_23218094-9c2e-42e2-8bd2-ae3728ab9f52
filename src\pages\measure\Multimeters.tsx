import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  ArrowRight,
  Gauge,
  Zap,
  Shield,
  FileText
} from "lucide-react";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Abstract shapes */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-yellow-100 rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-200 rounded-tr-full opacity-20"></div>
      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>
  );
};

const PDF_URL = "/T&M April 2025.pdf";

// Product Overview Card Component
const ProductCard = ({
  title,
  modelNumber,
  image,
  displayInfo,
  onViewDetailsClick
}: {
  title: string;
  modelNumber: string;
  image: string;
  displayInfo: string;
  onViewDetailsClick: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white border border-yellow-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 flex flex-col h-full"
      style={{ fontFamily: 'Open Sans, sans-serif' }}
    >
      {/* Model Number Badge */}
      <div className="flex justify-end p-3">
        <span className="bg-yellow-100 text-yellow-800 typography-extra-small px-3 py-1 rounded-full font-bold">
          {modelNumber}
        </span>
      </div>
      {/* Product Image */}
      <div className="flex items-center justify-center h-32 md:h-40 bg-yellow-50">
        <img
          src={image}
          alt={title}
          className="max-h-full max-w-full object-contain"
          onError={e => {
            e.currentTarget.onerror = null;
            e.currentTarget.src = 'https://via.placeholder.com/200x150/FFD700/000000?text=No+Image';
          }}
        />
      </div>
      <div className="p-4 flex-1 flex flex-col justify-between">
        <div className="text-center mb-2">
          <h3 className="typography-h6 text-gray-900 font-bold">{title}</h3>
          <div className="typography-extra-small text-gray-600 mt-1">{displayInfo}</div>
        </div>
        <button
          onClick={onViewDetailsClick}
          className="w-full bg-yellow-300 hover:bg-yellow-400 text-gray-900 typography-small py-2 px-4 rounded-lg transition-all duration-200 mt-4 flex items-center justify-center space-x-2"
        >
          <span>View Details</span>
          <ArrowRight className="inline h-4 w-4 ml-1" />
        </button>
      </div>
    </motion.div>
  );
};

// Hero Section (Clamp Meter style for Multimeter)
const HeroSection = ({ onRequestDemo, onViewBrochure }) => (
  <section className="relative min-h-[60vh] flex items-center justify-center py-6 md:py-12 overflow-hidden">
    {/* Background Elements */}
    <div className="absolute inset-0 z-0">
      <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
      <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      <div className="absolute top-20 right-20 w-32 h-32 bg-yellow-300 rounded-full opacity-20"></div>
    </div>
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 flex flex-col md:flex-row items-center justify-center w-full gap-10 md:gap-20">
      <div className="flex-1 text-center md:text-left">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8 flex flex-col items-center md:items-start justify-center w-full"
        >
          <div className="inline-block bg-yellow-400 px-6 py-3 rounded-full mb-4">
            <span className="text-gray-900 font-bold text-lg" style={{ fontFamily: 'Open Sans, sans-serif' }}>
              KRYKARD Digital Multimeter Solutions
            </span>
          </div>
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-bold text-gray-900 leading-tight mb-4" style={{ fontFamily: 'Open Sans, sans-serif' }}>
            DIGITAL MULTIMETERS
          </h1>
          <p className="text-base md:text-lg lg:text-lg text-black leading-relaxed font-medium max-w-2xl mx-auto md:mx-0 mb-8" style={{ fontFamily: 'Open Sans, sans-serif' }}>
            Precision-engineered digital multimeters for accurate, reliable, and versatile electrical measurements in every professional application.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center md:justify-start pt-2">
            <button
              className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base"
              onClick={onRequestDemo}
            >
              <span>Request Demo</span>
              <ArrowRight className="h-5 w-5" />
            </button>
            <button
              className="px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:bg-yellow-50 flex items-center justify-center space-x-3 text-base"
              onClick={onViewBrochure}
            >
              <span>View Brochure</span>
              <FileText className="h-5 w-5" />
            </button>
          </div>
        </motion.div>
      </div>
      <div className="flex-1 flex justify-center items-center">
        <img
          src="/multimeter/CA 5292.png"
          alt="Digital Multimeter Hero"
          className="max-h-72 md:max-h-96 w-auto object-contain drop-shadow-2xl rounded-2xl border-4 border-yellow-300 bg-white"
        />
      </div>
    </div>
  </section>
);

// Main Digital Multimeters Component
const DigitalMultimeters = () => {
  const navigate = useNavigate();

  // Handler for View Details button - Navigate to individual product pages
  const handleViewDetails = (productType: string) => {
    // Map old product types to new product IDs
    const productIdMap: { [key: string]: string } = {
      'basic': 'mtx203',
      'standard': 'dmm210',
      'advanced': 'dmm220',
      'ca_advanced': 'ca5273',
      'mtx_high': 'mtx3291',
      'professional': 'ca5292',
      'leakage': 'f65'
    };
    const productId = productIdMap[productType];
    if (productId) {
      navigate(`/measure/digital-multimeters/product/${productId}`);
    }
  };

  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };

  // Product Data
  const products = [
    {
      id: "basic",
      title: "Basic Digital Multimeter",
      modelNumber: "MTX 203",
      image: "/multimeter/MTX 203.png",
      displayInfo: "6000 counts LCD"
    },
    {
      id: "standard",
      title: "Standard Digital Multimeter",
      modelNumber: "DMM 210/220/230",
      image: "/multimeter/DMM 210.png",
      displayInfo: "6,000 counts LCD"
    },
    {
      id: "advanced",
      title: "Advanced Digital Multimeter",
      modelNumber: "DMM 240",
      image: "/multimeter/DMM 240.png",
      displayInfo: "40,000 counts LCD"
    },
    {
      id: "ca_advanced",
      title: "CA Advanced Series",
      modelNumber: "CA 5273/5275/5277",
      image: "/multimeter/CA 5273.png",
      displayInfo: "2 x 6,000 counts LCD"
    },
    {
      id: "mtx_high",
      title: "High Resolution Multimeter",
      modelNumber: "MTX 3291",
      image: "/multimeter/MTX 3291.png",
      displayInfo: "60,000 counts LCD"
    },
    {
      id: "professional",
      title: "Professional Digital Multimeter",
      modelNumber: "CA 5292/CA 5293",
      image: "/multimeter/CA 5292.png",
      displayInfo: "100,000 count color display"
    },
    {
      id: "leakage",
      title: "RMS Leakage Current Clamp",
      modelNumber: "F65",
      image: "/multimeter/F65.png",
      displayInfo: "10,000 counts LCD"
    }
  ];

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>
      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Hero Section */}
        <HeroSection onRequestDemo={handleRequestDemo} onViewBrochure={handleViewBrochure} />

        {/* Product Cards Section */}
        <section className="py-12 md:py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-10"
            >
              <div className="inline-block bg-yellow-100 text-gray-900 px-6 py-3 rounded-full text-lg font-bold mb-6">
                PROFESSIONAL SERIES
              </div>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Our Digital Multimeter Range
              </h2>
              <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-2">
                Advanced metering solutions for monitoring and analyzing electrical parameters with precision
              </p>
            </motion.div>
            {/* Responsive Product Card Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-8">
              {products.map(product => (
                <ProductCard
                  key={product.id}
                  title={product.title}
                  modelNumber={product.modelNumber}
                  image={product.image}
                  displayInfo={product.displayInfo}
                  onViewDetailsClick={() => handleViewDetails(product.id)}
                />
              ))}
            </div>
          </div>
        </section>
        {/* Key Features Section */}
        <KeyFeaturesSection />
        {/* Contact Section */}
        <ContactSection onContactClick={() => navigate('/contact/sales')} />
      </div>
    </PageLayout>
  );
};

// Key Features Section (Clamp Meter style)
const KeyFeaturesSection = () => (
  <section className="py-12 md:py-16 bg-gradient-to-br from-yellow-50 to-white">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="text-center mb-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 font-['Open_Sans']">Key Features</h2>
          <p className="mt-4 text-base md:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
            Discover the advanced capabilities that set our digital multimeters apart for professional and industrial use.
          </p>
        </motion.div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="rounded-2xl border border-yellow-200 hover:border-yellow-400 transition-all duration-300 p-6 h-full bg-white flex flex-col items-center text-center"
        >
          <div className="flex flex-row items-center justify-center gap-3 mb-4">
            <div className="bg-gradient-to-br from-yellow-400 to-yellow-500 w-12 h-12 rounded-2xl flex items-center justify-center">
              <Gauge className="h-7 w-7 text-gray-900" />
            </div>
            <h3 className="text-lg md:text-xl font-bold text-gray-900 mb-0">Precision Accuracy</h3>
          </div>
          <p className="text-gray-700 font-medium text-base">Highly accurate measurements with exceptional resolution, ensuring reliable readings for all professional applications.</p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
          className="rounded-2xl border border-yellow-200 hover:border-yellow-400 transition-all duration-300 p-6 h-full bg-white flex flex-col items-center text-center"
        >
          <div className="flex flex-row items-center justify-center gap-3 mb-4">
            <div className="bg-gradient-to-br from-yellow-400 to-yellow-500 w-12 h-12 rounded-2xl flex items-center justify-center">
              <Zap className="h-7 w-7 text-gray-900" />
            </div>
            <h3 className="text-lg md:text-xl font-bold text-gray-900 mb-0">Versatile Measurement</h3>
          </div>
          <p className="text-gray-700 font-medium text-base">Measure voltage, current, resistance, capacitance, frequency, and temperature with a single device for maximum flexibility.</p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          viewport={{ once: true }}
          className="rounded-2xl border border-yellow-200 hover:border-yellow-400 transition-all duration-300 p-6 h-full bg-white flex flex-col items-center text-center"
        >
          <div className="flex flex-row items-center justify-center gap-3 mb-4">
            <div className="bg-gradient-to-br from-yellow-400 to-yellow-500 w-12 h-12 rounded-2xl flex items-center justify-center">
              <Shield className="h-7 w-7 text-gray-900" />
            </div>
            <h3 className="text-lg md:text-xl font-bold text-gray-900 mb-0">Data Analysis & Storage</h3>
          </div>
          <p className="text-gray-700 font-medium text-base">Advanced models offer data logging, memory storage, and PC interfaces to help you track, analyze, and document your measurements.</p>
        </motion.div>
      </div>
    </div>
  </section>
);

const ContactSection = ({ onContactClick }) => (
  <section className="py-12 md:py-16 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200 mt-10 mb-24">
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
      >
        <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
          Need Expert Advice?
        </h2>
        <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
          Our specialists provide comprehensive guidance on digital multimeter solutions.
        </p>
        <button
          className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto mt-2"
          onClick={onContactClick}
        >
          <span>Contact Sales</span>
          <ArrowRight className="h-5 w-5" />
        </button>
      </motion.div>
    </div>
  </section>
);

export default DigitalMultimeters;