import React from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { Gauge, Zap, Database, Award, FileText, ArrowRight, Shield } from "lucide-react";

// Import insulationTesters data from the new product subpage
import { insulationTesters } from "./productpages/InsulationTesterProduct";

const PDF_URL = "/T&M April 2025.pdf";

const HeroSection = ({ onRequestDemo, onViewBrochure }: { onRequestDemo: () => void; onViewBrochure: () => void }) => (
  <section className="relative min-h-[60vh] flex items-center justify-center py-6 md:py-12 overflow-hidden">
    <div className="absolute inset-0 z-0">
      <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
      <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      <div className="absolute top-20 right-20 w-32 h-32 bg-yellow-300 rounded-full opacity-20"></div>
    </div>
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 flex flex-col items-center justify-center w-full">
      <div className="text-center w-full">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8 flex flex-col items-center justify-center w-full"
        >
          <div className="inline-block bg-yellow-400 px-6 py-3 rounded-full mb-4">
            <span className="text-black font-bold text-lg" style={{ fontFamily: 'Open Sans, sans-serif' }}>
              KRYKARD Insulation Testing Solutions
            </span>
          </div>
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-bold text-black leading-tight mb-4" style={{ fontFamily: 'Open Sans, sans-serif' }}>
            INSULATION TESTERS
          </h1>
          <p className="text-base md:text-lg lg:text-lg text-black leading-relaxed font-medium max-w-4xl mx-auto mb-8" style={{ fontFamily: 'Open Sans, sans-serif' }}>
            Professional-grade insulation testers for precise measurements and reliable results in electrical maintenance and troubleshooting.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center pt-2">
            <Button
              className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base"
              onClick={onRequestDemo}
            >
              Request Demo
            </Button>
            <Button
              className="px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 hover:bg-yellow-50 hover:border-yellow-500 flex items-center justify-center space-x-3 text-base"
              onClick={onViewBrochure}
            >
              View Brochure
              <FileText className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  </section>
);

// Product Overview Card Component (following multimeter design pattern)
const ProductCard = ({
  title,
  modelNumber,
  image,
  displayInfo,
  onViewDetailsClick
}: {
  title: string;
  modelNumber: string;
  image: string;
  displayInfo: string;
  onViewDetailsClick: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white border border-yellow-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 flex flex-col h-full"
      style={{ fontFamily: 'Open Sans, sans-serif' }}
    >
      {/* Model Number Badge */}
      <div className="flex justify-end p-3">
        <span className="bg-yellow-100 text-yellow-800 text-xs font-semibold px-3 py-1 rounded-full">
          {modelNumber}
        </span>
      </div>
      {/* Product Image */}
      <div className="flex items-center justify-center h-32 md:h-40 bg-yellow-50">
        <img
          src={image}
          alt={title}
          className="h-24 md:h-32 object-contain"
        />
      </div>
      <div className="p-4 flex-1 flex flex-col justify-between">
        <div className="text-center mb-2">
          <h3 className="text-base font-bold text-black">{title}</h3>
          <div className="text-xs text-gray-600 mt-1">{displayInfo}</div>
        </div>
        <button
          onClick={onViewDetailsClick}
          className="w-full bg-yellow-300 hover:bg-yellow-400 text-gray-900 font-bold py-2 px-4 rounded-lg transition-all duration-200 mt-4 flex items-center justify-center space-x-2"
        >
          <span>View Details</span>
          <ArrowRight className="inline h-4 w-4 ml-1" />
        </button>
      </div>
    </motion.div>
  );
};

// Contact Section Component with proper spacing to prevent footer overlap
const ContactSection = ({ onContactClick }: { onContactClick: () => void }) => (
  <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-6 px-4 rounded-2xl shadow-lg font-['Open_Sans'] mt-12 mb-8 md:mb-12">
    <div className="max-w-full mx-auto text-center pb-6">
      <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-black mb-3 font-['Open_Sans']">Need More Information?</h2>
      <p className="text-black mb-6 max-w-4xl mx-auto font-semibold text-sm md:text-base lg:text-lg text-center font-['Open_Sans']">
        Our team of experts is ready to help you with product specifications, custom solutions,
        pricing, and any other details you need about KRYKARD Insulation Testers.
      </p>
      <Button
        className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1 text-sm md:text-base"
        onClick={onContactClick}
      >
        Contact Our Experts
        <ArrowRight className="ml-2 h-4 md:h-5 w-4 md:w-5" />
      </Button>
    </div>
  </div>
);

const KeyFeaturesSection = () => (
  <div className="py-6 md:py-8 bg-gradient-to-br from-yellow-50 to-white font-['Open_Sans']">
    <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
      <div className="text-center mb-6 md:mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-black mb-3 font-['Open_Sans']">Why Choose Our Insulation Testers?</h2>
          <p className="mt-3 text-sm md:text-base lg:text-lg text-gray-800 max-w-4xl mx-auto font-medium text-center font-['Open_Sans']">
            Professional insulation testers designed for precision and reliability in the most demanding environments
          </p>
        </motion.div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        <FeatureHighlight
          icon={<Shield size={24} className="text-white" />}
          title="Superior Accuracy"
          description="High-precision testing with accuracy up to ±3% for reliable insulation resistance measurements."
        />
        <FeatureHighlight
          icon={<Zap size={24} className="text-white" />}
          title="Versatile Voltage Ranges"
          description="Test voltages from 10V to 15kV to cover all your insulation testing requirements."
        />
        <FeatureHighlight
          icon={<Award size={24} className="text-white" />}
          title="Professional-Grade Quality"
          description="Robust construction and reliability for industrial and field applications."
        />
        <FeatureHighlight
          icon={<Database size={24} className="text-white" />}
          title="Advanced Data Storage"
          description="Store up to 80,000 measurement points with easy data retrieval and analysis."
        />
        <FeatureHighlight
          icon={<Gauge size={24} className="text-white" />}
          title="Comprehensive Analysis"
          description="Automatic calculation of DAR/PI/DD ratios for detailed insulation health assessment."
        />
        <FeatureHighlight
          icon={<FileText size={24} className="text-white" />}
          title="Time-Saving Features"
          description="Programmable test durations, timers, and auto-saving for efficient workflow."
        />
      </div>
    </div>
  </div>
);

const FeatureHighlight = ({ icon, title, description }: { icon: React.ReactNode; title: string; description: string }) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true }}
    whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
    className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-4 h-full border-b-4 border-yellow-400 font-['Open_Sans']"
  >
    <div className="flex flex-col h-full text-center md:text-left">
      <div className="bg-gradient-to-br from-yellow-400 to-yellow-300 w-12 h-12 rounded-lg flex items-center justify-center mb-3 shadow-md mx-auto md:mx-0">
        {icon}
      </div>
      <h3 className="text-base md:text-lg lg:text-xl font-bold text-black mb-2 font-['Open_Sans']">{title}</h3>
      <p className="text-gray-900 flex-grow font-medium text-sm md:text-base text-center md:text-justify font-['Open_Sans']">{description}</p>
    </div>
  </motion.div>
);

const InsulationTesters = () => {
  const navigate = useNavigate();
  const handleRequestDemo = () => navigate("/contact/sales");
  const handleViewBrochure = () => window.open(PDF_URL, '_blank');
  const handleViewDetails = (id: number) => navigate(`/measure/insulation-testers/product/${id}`);
  const products = Object.values(insulationTesters);

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      <div className="font-['Open_Sans']">
        <HeroSection onRequestDemo={handleRequestDemo} onViewBrochure={handleViewBrochure} />
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8 font-['Open_Sans']">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6 md:mb-8"
          >
            <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm md:text-base font-semibold mb-3 font-['Open_Sans']">
              PROFESSIONAL SERIES
            </span>
            <h2 className="text-xl md:text-2xl lg:text-4xl font-bold mb-4 text-black font-['Open_Sans']">
              Complete Insulation Tester Range
            </h2>
            <p className="max-w-4xl mx-auto text-gray-800 text-sm md:text-base lg:text-lg font-medium text-center font-['Open_Sans']">
              Explore our comprehensive lineup of professional insulation testers for various voltage ranges and applications
            </p>
          </motion.div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-8">
            {products.map(product => (
              <ProductCard
                key={product.id}
                title={product.subtitle}
                modelNumber={product.model}
                image={product.image}
                displayInfo={product.title}
                onViewDetailsClick={() => handleViewDetails(product.id)}
              />
            ))}
          </div>
        </div>
        {/* Key Features Section after product cards */}
        <KeyFeaturesSection />
        {/* Contact Section at the end */}
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <ContactSection onContactClick={handleRequestDemo} />
        </div>
      </div>
    </PageLayout>
  );
};

export default InsulationTesters;