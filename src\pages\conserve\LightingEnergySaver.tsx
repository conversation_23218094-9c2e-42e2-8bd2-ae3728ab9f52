import React, { useState, useEffect, useRef } from 'react';
import {
  Users,
  Shield,
  CheckCircle,
  Building,
  TrendingUp,
  Eye,
  Monitor,

  Zap,
  Droplet,
  Flame,
  CreditCard,
  BarChart,
  DollarSign,
  PiggyBank,
  Smartphone,
  Award,
  ChevronDown
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import PageLayout from "@/components/layout/PageLayout";

const TenantBillingSolutionPage = () => {
  const navigate = useNavigate();

  const [countsStarted, setCountsStarted] = useState(false);
  const [expandedCard, setExpandedCard] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('features');
  const statsRef = useRef<HTMLDivElement>(null);

  const stats = [
    { label: 'Billing Accuracy', value: '99.9%', displayValue: '99.9%', icon: TrendingUp, color: 'from-green-500 to-emerald-600' },
    { label: 'Cost Reduction', value: '25-40%', displayValue: '25-40%', icon: Award, color: 'from-emerald-500 to-teal-600' },
    { label: 'Active Tenants', value: '500+', displayValue: '500+', countTo: 500, icon: Monitor, color: 'from-teal-500 to-green-600' },
    { label: 'Monthly Reports', value: '1000+', displayValue: '1000+', countTo: 1000, icon: BarChart, color: 'from-green-600 to-emerald-700' }
  ];

  // Count-up animation state
  const [tenantsCount, setTenantsCount] = useState(0);
  const [reportsCount, setReportsCount] = useState(0);

  // Intersection Observer for triggering count-up
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !countsStarted) {
          setCountsStarted(true);

          // Animate tenants count
          const tenantsTarget = 500;
          const tenantsDuration = 2000;
          const tenantsStartTime = Date.now();

          const animateTenants = () => {
            const elapsed = Date.now() - tenantsStartTime;
            const progress = Math.min(elapsed / tenantsDuration, 1);
            const current = Math.floor(progress * tenantsTarget);
            setTenantsCount(current);

            if (progress < 1) {
              requestAnimationFrame(animateTenants);
            }
          };

          // Animate reports count
          const reportsTarget = 1000;
          const reportsDuration = 2500;
          const reportsStartTime = Date.now();

          const animateReports = () => {
            const elapsed = Date.now() - reportsStartTime;
            const progress = Math.min(elapsed / reportsDuration, 1);
            const current = Math.floor(progress * reportsTarget);
            setReportsCount(current);

            if (progress < 1) {
              requestAnimationFrame(animateReports);
            }
          };

          // Start animations
          requestAnimationFrame(animateTenants);
          requestAnimationFrame(animateReports);
        }
      },
      { threshold: 0.3 }
    );

    if (statsRef.current) {
      observer.observe(statsRef.current);
    }

    return () => observer.disconnect();
  }, [countsStarted]);



  return (
    <div className="min-h-screen w-full overflow-x-hidden bg-[#e6f7f1]">
      <PageLayout
        hideHero={true}
        hideBreadcrumbs={true}
      >
        <style>{`
          .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(3deg); }
          }
          @keyframes float-delayed {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-12px) rotate(-2deg); }
          }
          .animate-float {
            animation: float 5s ease-in-out infinite;
          }
          .animate-float-delayed {
            animation: float-delayed 6s ease-in-out infinite;
          }
        `}</style>
        {/* Main Title Section with 3D/Blurred Background Elements */}
        <div className="relative bg-green-100 py-6 sm:py-8 lg:py-10 overflow-hidden">
          {/* 3D/Blurred Background Elements */}
          <div className="absolute inset-0 pointer-events-none z-0">
            <div className="absolute -top-10 -left-10 w-40 h-40 bg-green-300 opacity-30 rounded-full blur-3xl animate-float" />
            <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-emerald-200 opacity-20 rounded-full blur-2xl animate-float-delayed" />
            <div className="absolute -bottom-10 right-0 w-48 h-48 bg-green-200 opacity-25 rounded-full blur-3xl animate-float" />
            <div className="absolute top-10 right-1/4 w-24 h-24 bg-emerald-300 opacity-15 rounded-full blur-2xl animate-float-delayed" />
            <div className="absolute bottom-1/3 left-1/4 w-28 h-28 bg-green-400 opacity-10 rounded-full blur-2xl animate-float" />
          </div>
          {/* Main Content */}
          <div className="relative max-w-7xl mx-auto text-center px-4 sm:px-6 lg:px-8 z-20">
            <h1 className="typography-h1 font-bold text-green-900 mb-4">
              Tenent Biling Solution
            </h1>
            <p className="typography-body text-black max-w-4xl mx-auto leading-relaxed">
            Empowering Property Managers with Automated, Transparent, and Accurate Utility Billing for Every Tenant.
            </p>
            {/* <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-32 sm:w-40 h-1 bg-green-600 rounded-full shadow-lg"></div> */}
          </div>
        </div>
        {/* Modern Blended Hero Section */}
        <div className="relative overflow-hidden">
          {/* Enhanced Seamless Background Blend */}
          <div className="absolute inset-0 bg-[#e6f7f1]"></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-green-100/30 via-transparent to-emerald-100/20"></div>

          {/* Enhanced Organic Floating Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-20 left-2 w-32 h-32 sm:w-48 sm:h-48 bg-gradient-to-br from-green-200/30 to-emerald-300/20 rounded-full blur-2xl animate-pulse"></div>
            <div className="absolute bottom-10 right-10 w-80 h-80 bg-gradient-to-br from-emerald-300/35 to-teal-400/25 rounded-full blur-2xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-br from-teal-300/30 to-green-400/20 rounded-full blur-xl animate-pulse delay-500"></div>
            <div className="absolute top-1/2 left-1/3 w-48 h-48 bg-gradient-to-br from-green-200/25 to-emerald-300/15 rounded-full blur-2xl animate-pulse delay-2000"></div>
          </div>

          {/* Enhanced Subtle Grid Pattern */}
          <div className="absolute inset-0 opacity-[0.03]">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%23059669' stroke-width='1.5'%3E%3Cpath d='M0 0h100v100H0z'/%3E%3Cpath d='M0 50h100M50 0v100'/%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative px-4 sm:px-6 lg:px-8 py-8 lg:py-12">
            <div className="max-w-7xl mx-auto">
              <div className="grid lg:grid-cols-12 gap-8 lg:gap-10 items-center">
                {/* Left Content - 7 columns */}
                <div className="lg:col-span-7 text-center lg:text-left space-y-8">


                  {/* Main Heading - Vertical Layout */}
                  <div className="space-y-3 sm:space-y-4">
                    <h1 className="typography-h1 font-bold uppercase tracking-widest text-black">ALENSOFT</h1>
                    <h2 className="typography-h2 font-semibold text-black">Tenant Billing Solution</h2>
                  </div>
                  {/* Enhanced Description */}
                  <p className="typography-body text-gray-800 leading-relaxed font-semibold max-w-2xl mx-auto lg:mx-0 font-['Open_Sans']">
                    Transform your multi-tenant billing with our comprehensive platform designed for
                    <span className="text-green-700 font-black"> accurate cost allocation</span>,
                    <span className="text-green-700 font-black"> automated billing</span>, and
                    <span className="text-green-700 font-black"> transparent reporting</span>
                  </p>

                  {/* Enhanced Key Highlights */}
                  <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
                    <div className="flex items-center space-x-2 bg-white/95 backdrop-blur-sm px-5 py-3 rounded-full border border-green-300 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                      <CheckCircle className="w-5 h-5 text-green-700" />
                      <span className="typography-body font-normal text-black font-['Open_Sans']">Accurate Billing</span>
                    </div>
                    <div className="flex items-center space-x-2 bg-white/95 backdrop-blur-sm px-5 py-3 rounded-full border border-emerald-300 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                      <CheckCircle className="w-5 h-5 text-emerald-700" />
                      <span className="typography-body font-normal text-black font-['Open_Sans']">Multi-Tenant Support</span>
                    </div>
                    <div className="flex items-center space-x-2 bg-white/95 backdrop-blur-sm px-5 py-3 rounded-full border border-teal-300 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                      <CheckCircle className="w-5 h-5 text-teal-700" />
                      <span className="typography-body font-normal text-black font-['Open_Sans']">Automated Reports</span>
                    </div>
                  </div>
                </div>

                {/* Right Content - 5 columns */}
                <div
                  className="lg:col-span-5 relative flex items-center justify-center h-full"
                  style={{
                    backgroundImage: "url('/Tenant Billing/1 Billing Dsb.png')",
                    backgroundSize: 'contain',
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'center',
                    filter: 'brightness(1.18) contrast(1.08)',
                    minHeight: '18rem',
                    width: '100%',
                  }}
                >
                  {/* Enhanced Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-green-900/10 via-emerald-900/5 to-transparent pointer-events-none rounded-3xl"></div>
                  {/* Floating Elements on Image */}
                  <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md">
                    <Zap className="w-4 h-4 text-green-600" />
                  </div>
                  <div className="absolute bottom-2 left-2 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1 shadow-md">
                    <span className="typography-body font-normal text-black font-['Open_Sans']">Smart Billing</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Smart Tenant Billing Content Section */}
        <div className="py-8 sm:py-12 bg-[#e6f7f1] relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200/20 rounded-full blur-3xl"></div>
          </div>

          {/* Subtle Grid Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Main Heading */}
            <div className="text-center mb-8">
              <h2 className="typography-h1 font-bold text-black mb-6 font-['Open_Sans']">
                Scale your business and drive tenant satisfaction with our Smart Tenant Billing Solution!
              </h2>
            </div>

            {/* Description Content */}
            <div className="space-y-6 mb-8">
              <div className="bg-white/90 backdrop-blur-sm border border-green-200 rounded-xl p-6 shadow-lg hover:shadow-xl hover:border-green-300 transition-all duration-300">
                <p className="typography-body text-black leading-relaxed font-bold font-['Open_Sans']">
                  We offer a seamless and stress-free Utility billing system to automate billing for Residential and Commercial establishments.
                </p>
              </div>

              <div className="bg-white/90 backdrop-blur-sm border border-emerald-200 rounded-xl p-6 shadow-lg hover:shadow-xl hover:border-emerald-300 transition-all duration-300">
                <p className="typography-body text-black leading-relaxed font-bold font-['Open_Sans']">
                  Tenants that occupy multiple spaces with different meters can be combined into one single bill per utility type. The bill can be designed with the property's branding, payment options and configured to automatically email to a designated recipient.
                </p>
              </div>
            </div>

            {/* Utility Types Section */}
            <div className="text-center mb-6">
              <h3 className="typography-h2 font-semibold text-black mb-8 font-['Open_Sans']">
                Utility Types: Electricity, Water, Gas or any other Utility with Smart Meters
              </h3>
            </div>

            {/* Utility Icons - Horizontal Layout */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {/* Electricity */}
              <div className="group">
                <div className="bg-white/90 backdrop-blur-sm border border-green-200 rounded-2xl p-6 text-center shadow-lg hover:shadow-2xl hover:border-green-300 transition-all duration-300 transform hover:-translate-y-2">
                  <div className="bg-gradient-to-br from-yellow-500 to-orange-600 w-16 h-16 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform shadow-lg">
                    <Zap className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="typography-h4 font-semibold text-black mb-3 font-['Open_Sans']">Electricity</h4>
                  <div className="w-12 h-1 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-full mx-auto"></div>
                </div>
              </div>

              {/* Water */}
              <div className="group">
                <div className="bg-white/90 backdrop-blur-sm border border-green-200 rounded-2xl p-6 text-center shadow-lg hover:shadow-2xl hover:border-green-300 transition-all duration-300 transform hover:-translate-y-2">
                  <div className="bg-gradient-to-br from-blue-500 to-cyan-600 w-16 h-16 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform shadow-lg">
                    <Droplet className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="typography-h4 font-semibold text-black mb-3 font-['Open_Sans']">Water</h4>
                  <div className="w-12 h-1 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-full mx-auto"></div>
                </div>
              </div>

              {/* Gas */}
              <div className="group">
                <div className="bg-white/90 backdrop-blur-sm border border-green-200 rounded-2xl p-6 text-center shadow-lg hover:shadow-2xl hover:border-green-300 transition-all duration-300 transform hover:-translate-y-2">
                  <div className="bg-gradient-to-br from-red-500 to-orange-600 w-16 h-16 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform shadow-lg">
                    <Flame className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="typography-h4 font-semibold text-black mb-3 font-['Open_Sans']">Gas</h4>
                  <div className="w-12 h-1 bg-gradient-to-r from-red-500 to-orange-600 rounded-full mx-auto"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

       {/* Stats Section - Responsive Format */}
<div ref={statsRef} className="py-8 sm:py-10 bg-white relative overflow-hidden">
  {/* Background Pattern */}
  <div className="absolute inset-0 opacity-5">
    <div className="absolute inset-0" style={{
      backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
    }}></div>
  </div>

  <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div className="text-center mb-6 sm:mb-8">
      <h2 className="typography-h1 font-bold text-black mb-4 font-['Open_Sans']">
        Billing Performance
      </h2>
      <p className="typography-h3 font-normal text-black max-w-2xl mx-auto font-['Open_Sans']">
        Experience exceptional results with our smart tenant billing solutions
      </p>
    </div>

    {/* Desktop Table Layout (lg and above) */}
    <div className="hidden lg:block">
      <div className="bg-white rounded-xl sm:rounded-2xl shadow-2xl border-2 border-green-200 overflow-hidden max-w-4xl mx-auto hover:shadow-3xl transition-all duration-500">
        {/* Enhanced Table Header Row */}
        <div className="grid grid-cols-4 bg-gradient-to-r from-green-100 via-emerald-100 to-teal-100 border-b-2 border-green-200">
          {stats.map((stat, index) => (
            <div key={index} className="p-4 text-center border-r border-green-200 last:border-r-0 hover:bg-green-200/50 transition-all duration-300">
              <h3 className="typography-h4 font-semibold text-black leading-tight font-['Open_Sans']">
                {stat.label}
              </h3>
            </div>
          ))}
        </div>

        {/* Enhanced Table Values Row */}
        <div className="grid grid-cols-4 bg-white">
          {stats.map((stat, index) => {
            const getDisplayValue = () => {
              if (stat.countTo === 500) {
                return countsStarted ? `${tenantsCount}+` : '0+';
              } else if (stat.countTo === 1000) {
                return countsStarted ? `${reportsCount}+` : '0+';
              }
              return stat.displayValue;
            };

            return (
              <div key={index} className="p-6 text-center border-r border-green-200 last:border-r-0 group hover:bg-gradient-to-br hover:from-green-50 hover:to-emerald-50 transition-all duration-300 transform hover:scale-105">
                <div className="text-4xl sm:text-5xl font-bold text-green-700 group-hover:text-green-800 transition-all duration-300 font-['Open_Sans']">
                  {getDisplayValue()}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>

    {/* Mobile & Tablet Box Layout (below lg) */}
    <div className="lg:hidden">
      <div className="grid grid-cols-2 gap-4 sm:gap-6 max-w-2xl mx-auto">
        {stats.map((stat, index) => {
          const getDisplayValue = () => {
            if (stat.countTo === 500) {
              return countsStarted ? `${tenantsCount}+` : '0+';
            } else if (stat.countTo === 1000) {
              return countsStarted ? `${reportsCount}+` : '0+';
            }
            return stat.displayValue;
          };

          return (
            <div key={index} className="bg-white rounded-xl sm:rounded-2xl shadow-2xl border-2 border-green-200 p-4 sm:p-6 text-center group hover:shadow-3xl transition-all duration-500 transform hover:scale-105">
              {/* Label */}
              <div className="mb-3 sm:mb-4 bg-gradient-to-r from-green-100 via-emerald-100 to-teal-100 rounded-lg p-2 sm:p-3">
                <h3 className="typography-h4 font-semibold text-black leading-tight font-['Open_Sans']">
                  {stat.label}
                </h3>
              </div>

              {/* Value */}
              <div className="text-2xl sm:text-3xl md:text-4xl font-bold text-green-700 group-hover:text-green-800 transition-all duration-300 font-['Open_Sans']">
                {getDisplayValue()}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  </div>
</div>

        {/* ALENSOFT Tenant Billing Process Section - Modernized */}
        <div className="py-12 sm:py-16 bg-white border-t border-green-100">
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center">
              {/* Left Side - Bulleted List */}
              <div className="text-left">
                <h2 className="typography-h1 font-bold text-black mb-8 font-['Open_Sans']">
                  ALENSOFT Tenant Billing Process
                </h2>
                <ul className="list-disc pl-6 space-y-4 text-black font-normal typography-body font-['Open_Sans']">
                  <li>Each flat or unit can be metered - electricity, water or gas. Each flat may have multiple meters connected.</li>
                  <li>Each meter will send its data to a SMART gateway via data cables. Each floor may have multiple SMART Gateways.</li>
                  <li>All gateways send their data to the ALENSOFT server (on-premise or on-cloud).</li>
                  <li>ALENSOFT collates the data, analyses it and generates automated bills.</li>
                </ul>
              </div>
              {/* Right Side - Image */}
              <div className="flex justify-center items-center w-full">
                <img
                  src="/Tenant Billing/3 Bill.png"
                  alt="Alensoft Tenant Billing Process"
                  className="rounded-2xl shadow-2xl border-2 border-green-200 w-full max-w-md object-contain bg-white"
                  style={{ background: 'linear-gradient(135deg, #e0f7ef 0%, #f0fdfa 100%)', display: 'block' }}
                  loading="lazy"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Key Features of Tenant Billing Software Section */}
        <div className="py-12 sm:py-16 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 relative">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200/20 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-8 sm:mb-10">
              <h2 className="typography-h1 font-bold text-black mb-6 sm:mb-8 font-['Open_Sans']">
                Key Features of Tenant Billing Software
              </h2>
              <p className="typography-body text-black max-w-3xl mx-auto font-normal font-['Open_Sans']">
                Comprehensive tenant billing capabilities designed for modern property management
              </p>
            </div>

            {/* Core Features Grid - 3 top, 3 middle, 2 bottom, Tenant Management first */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-8 sm:mb-12">
              {/* Card 1: AI Self-Care Tenant Mobile App & Web Portal */}
              <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit">
                {/* AI Self-Care Tenant Mobile App & Web Portal */}
                <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                  <div className="bg-gradient-to-br from-green-600 to-emerald-700 p-2 rounded-lg shadow-md">
                    <Smartphone className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="typography-h4 font-semibold text-black font-['Open_Sans']">AI Self-Care Tenant Mobile App & Web Portal</h4>
                  </div>
                  <button onClick={() => setExpandedCard(expandedCard === 'selfcare' ? null : 'selfcare')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                    <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'selfcare' ? 'rotate-180' : ''}`} />
                  </button>
                </div>
                <p className="text-black font-normal typography-body font-['Open_Sans'] mb-3">Comprehensive self-service portal with AI-powered assistance for tenants.</p>
                {expandedCard === 'selfcare' && (
                  <div className="space-y-1 pt-2 border-t border-green-100">
                    <div className="flex items-start space-x-1">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1 flex-shrink-0"></div>
                      <p className="text-black font-normal typography-body font-['Open_Sans']">Give tenants control over their costs and consumption. The Tenant Self-Care mobile App / web portal comes complete with a customisable insights dashboard and access to online data and previous bills.</p>
                    </div>
                    <div className="flex items-start space-x-1">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1 flex-shrink-0"></div>
                      <p className="text-black font-normal typography-body font-['Open_Sans']">A chatbot is integrated directly into the Portal, ready to assist customers 24/7. It can handle inquiries, clarify billing details, explain contract terms, and assist users with submitting requests — all automatically, without the need for human involvement.</p>
                    </div>
                  </div>
                )}
              </div>
              {/* Card 2: Automated Meter Reading */}
              <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit">
                {/* Automated Meter Reading */}
                <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                  <div className="bg-gradient-to-br from-green-500 to-emerald-600 p-2 rounded-lg shadow-md">
                    <Monitor className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="typography-h4 font-semibold text-black font-['Open_Sans']">Automated Meter Reading</h4>
                  </div>
                  <button onClick={() => setExpandedCard(expandedCard === 'meter' ? null : 'meter')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                    <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'meter' ? 'rotate-180' : ''}`} />
                  </button>
                </div>
                <p className="text-black font-normal typography-body font-['Open_Sans'] mb-3">Automatic consumption data collection from smart meters for error-free invoicing.</p>
                {expandedCard === 'meter' && (
                  <div className="pt-2 border-t border-green-100">
                    <p className="text-black font-normal typography-body font-['Open_Sans']">Alensoft automatically reads utility consumption data from tenant Smart meters, so it's easy to generate error-free invoices.</p>
                  </div>
                )}
              </div>
              {/* Card 3: Anomaly Detection */}
              <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit">
                {/* Anomaly Detection */}
                <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                  <div className="bg-gradient-to-br from-emerald-500 to-teal-600 p-2 rounded-lg shadow-md">
                    <Eye className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="typography-h4 font-semibold text-black font-['Open_Sans']">Anomaly Detection</h4>
                  </div>
                  <button onClick={() => setExpandedCard(expandedCard === 'anomaly' ? null : 'anomaly')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                    <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'anomaly' ? 'rotate-180' : ''}`} />
                  </button>
                </div>
                <p className="text-black font-normal typography-body font-['Open_Sans'] mb-3">Identify consumption trends and anomalies with instant alerts for abnormalities.</p>
                {expandedCard === 'anomaly' && (
                  <div className="pt-2 border-t border-green-100">
                    <p className="text-black font-normal typography-body font-['Open_Sans']">Find out where energy is being used, recognise trends and anomalies and identify opportunities to reduce consumption. Get periodical instant alerts in case of any abnormalities.</p>
                  </div>
                )}
              </div>
              {/* Card 4: Real-time Visibility & Invoice Generation */}
              <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit">
                <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                  <div className="bg-gradient-to-br from-teal-500 to-green-600 p-2 rounded-lg shadow-md">
                    <BarChart className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="typography-h4 font-semibold text-black font-['Open_Sans']">Real-time Visibility & Invoice Generation</h4>
                  </div>
                  <button onClick={() => setExpandedCard(expandedCard === 'realtime' ? null : 'realtime')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                    <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'realtime' ? 'rotate-180' : ''}`} />
                  </button>
                </div>
                <p className="text-black font-normal typography-body font-['Open_Sans'] mb-3">Comprehensive real-time monitoring and automated invoice generation system.</p>
                {expandedCard === 'realtime' && (
                  <div className="space-y-1 pt-2 border-t border-green-100">
                    <div className="flex items-start space-x-1">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1 flex-shrink-0"></div>
                      <p className="text-black font-bold text-xs font-['Open_Sans']">Real-time visibility of utility consumption and bill generation based on the service period configured.</p>
                    </div>
                    <div className="flex items-start space-x-1">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1 flex-shrink-0"></div>
                      <p className="text-black font-bold text-xs font-['Open_Sans']">Automating the creation of invoices for utilities consumed, and other charges. Maintain a full historical record of unpaid and paid invoices.</p>
                    </div>
                    <div className="flex items-start space-x-1">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1 flex-shrink-0"></div>
                      <p className="text-black font-bold text-xs font-['Open_Sans']">Generate invoices in any format (PDF, Word, Excel, image) and send them to individual tenants or groups of tenants within a property. The invoice can be designed with the property's branding, and payment options can be set up to automatically email a designated recipient.</p>
                    </div>
                  </div>
                )}
              </div>
              {/* Card 5: Payment Tracking */}
              <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit">
                <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                  <div className="bg-gradient-to-br from-green-600 to-emerald-700 p-2 rounded-lg shadow-md">
                    <CreditCard className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-base sm:text-lg font-black text-black font-['Open_Sans']">Payment Tracking</h4>
                  </div>
                  <button onClick={() => setExpandedCard(expandedCard === 'payment' ? null : 'payment')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                    <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'payment' ? 'rotate-180' : ''}`} />
                  </button>
                </div>
                <p className="text-black font-bold text-xs sm:text-sm font-['Open_Sans'] mb-2">Comprehensive tracking of both pre-paid and post-paid payment options.</p>
                {expandedCard === 'payment' && (
                  <div className="pt-2 border-t border-green-100">
                    <p className="text-black font-bold text-xs font-['Open_Sans']">Facilitates tracking of both Pre-paid and Post-paid options.</p>
                  </div>
                )}
              </div>
              {/* Card 6: Reporting and Analytics */}
              <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit">
                <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                  <div className="bg-gradient-to-br from-emerald-600 to-teal-700 p-2 rounded-lg shadow-md">
                    <BarChart className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-base sm:text-lg font-black text-black font-['Open_Sans']">Reporting and Analytics</h4>
                  </div>
                  <button onClick={() => setExpandedCard(expandedCard === 'reporting' ? null : 'reporting')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                    <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'reporting' ? 'rotate-180' : ''}`} />
                  </button>
                </div>
                <p className="text-black font-bold text-xs sm:text-sm font-['Open_Sans'] mb-2">Online historical reports and analytics on tenant billing data.</p>
                {expandedCard === 'reporting' && (
                  <div className="pt-2 border-t border-green-100">
                    <p className="text-black font-bold text-xs font-['Open_Sans']">Providing online historical reports and analytics on tenant billing data and outstanding payments.</p>
                  </div>
                )}
              </div>
              {/* Card 7: Integration with Other Systems */}
              <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit col-span-1 lg:col-start-2">
                <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                  <div className="bg-gradient-to-br from-teal-500 to-green-600 p-2 rounded-lg shadow-md">
                    <Building className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-base sm:text-lg font-black text-black font-['Open_Sans']">Integration with Other Systems</h4>
                  </div>
                  <button onClick={() => setExpandedCard(expandedCard === 'integration' ? null : 'integration')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                    <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'integration' ? 'rotate-180' : ''}`} />
                  </button>
                </div>
                <p className="text-black font-bold text-xs sm:text-sm font-['Open_Sans'] mb-2">Seamless integration with existing business systems and platforms.</p>
                {expandedCard === 'integration' && (
                  <div className="pt-2 border-t border-green-100">
                    <p className="text-black font-bold text-xs font-['Open_Sans']">Integrating with Building Management Software (BMS), payment gateways, accounting software, and other relevant systems.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Benefits Section */}
            <div className="mb-8 sm:mb-12">
              <div className="text-center mb-6 sm:mb-8">
                <h3 className="typography-h2 font-bold text-black mb-6 sm:mb-8 font-['Open_Sans']">
                  Benefits of using ALENSOFT Tenant Billing Software Solution
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                {/* Increased Efficiency */}
                <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit">
                  <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                    <div className="bg-gradient-to-br from-green-500 to-emerald-600 p-2 rounded-lg shadow-md">
                      <TrendingUp className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-base sm:text-lg font-black text-black font-['Open_Sans']">Increased Efficiency</h4>
                    </div>
                    <button onClick={() => setExpandedCard(expandedCard === 'efficiency' ? null : 'efficiency')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                      <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'efficiency' ? 'rotate-180' : ''}`} />
                    </button>
                  </div>
                  <p className="text-black font-bold text-xs sm:text-sm font-['Open_Sans'] mb-2">Streamlined automation for enhanced operational efficiency.</p>
                  {expandedCard === 'efficiency' && (
                    <div className="pt-2 border-t border-green-100">
                      <p className="text-black font-bold text-xs font-['Open_Sans']">Automating tasks like meter readings and invoice generation saves time and reduces manual effort.</p>
                    </div>
                  )}
                </div>
                {/* Reduced Errors */}
                <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit">
                  <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                    <div className="bg-gradient-to-br from-emerald-500 to-teal-600 p-2 rounded-lg shadow-md">
                      <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-base sm:text-lg font-black text-black font-['Open_Sans']">Reduced Errors</h4>
                    </div>
                    <button onClick={() => setExpandedCard(expandedCard === 'errors' ? null : 'errors')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                      <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'errors' ? 'rotate-180' : ''}`} />
                    </button>
                  </div>
                  <p className="text-black font-bold text-xs sm:text-sm font-['Open_Sans'] mb-2">Minimized billing and payment errors through automation.</p>
                  {expandedCard === 'errors' && (
                    <div className="pt-2 border-t border-green-100">
                      <p className="text-black font-bold text-xs font-['Open_Sans']">Automated systems minimize the risk of errors in billing and payment.</p>
                    </div>
                  )}
                </div>
                {/* Improved Cash Flow */}
                <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit">
                  <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                    <div className="bg-gradient-to-br from-teal-500 to-green-600 p-2 rounded-lg shadow-md">
                      <DollarSign className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-base sm:text-lg font-black text-black font-['Open_Sans']">Improved Cash Flow</h4>
                    </div>
                    <button onClick={() => setExpandedCard(expandedCard === 'cashflow' ? null : 'cashflow')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                      <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'cashflow' ? 'rotate-180' : ''}`} />
                    </button>
                  </div>
                  <p className="text-black font-bold text-xs sm:text-sm font-['Open_Sans'] mb-2">Enhanced financial management and payment processing.</p>
                  {expandedCard === 'cashflow' && (
                    <div className="pt-2 border-t border-green-100">
                      <p className="text-black font-bold text-xs font-['Open_Sans']">Faster payment tracking and reporting can improve cash flow and reduce the risk of late payments.</p>
                    </div>
                  )}
                </div>
                {/* Better Tenant Relations */}
                <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit">
                  <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                    <div className="bg-gradient-to-br from-green-600 to-emerald-700 p-2 rounded-lg shadow-md">
                      <Users className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-base sm:text-lg font-black text-black font-['Open_Sans']">Better Tenant Relations</h4>
                    </div>
                    <button onClick={() => setExpandedCard(expandedCard === 'relations' ? null : 'relations')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                      <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'relations' ? 'rotate-180' : ''}`} />
                    </button>
                  </div>
                  <p className="text-black font-bold text-xs sm:text-sm font-['Open_Sans'] mb-2">Enhanced tenant satisfaction through accurate billing practices.</p>
                  {expandedCard === 'relations' && (
                    <div className="pt-2 border-t border-green-100">
                      <p className="text-black font-bold text-xs font-['Open_Sans']">Accurate and timely billing can improve tenant satisfaction and reduce disputes.</p>
                    </div>
                  )}
                </div>
                {/* Cost Savings */}
                <div className="bg-white/95 backdrop-blur-sm border border-green-100 rounded-lg sm:rounded-xl p-4 sm:p-5 hover:shadow-xl hover:border-green-300 transition-all duration-400 transform hover:-translate-y-1 h-fit">
                  <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                    <div className="bg-gradient-to-br from-emerald-600 to-teal-700 p-2 rounded-lg shadow-md">
                      <PiggyBank className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-base sm:text-lg font-black text-black font-['Open_Sans']">Cost Savings</h4>
                    </div>
                    <button onClick={() => setExpandedCard(expandedCard === 'savings' ? null : 'savings')} className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                      <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${expandedCard === 'savings' ? 'rotate-180' : ''}`} />
                    </button>
                  </div>
                  <p className="text-black font-bold text-xs sm:text-sm font-['Open_Sans'] mb-2">Reduced operational costs through process automation.</p>
                  {expandedCard === 'savings' && (
                    <div className="pt-2 border-t border-green-100">
                      <p className="text-black font-bold text-xs font-['Open_Sans']">Automating the billing process can reduce overhead costs associated with manual billing.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Overall Capability Section - Simple and Attractive */}
        <div className="py-12 sm:py-16 bg-white border-t border-green-100">
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-8">
              <h2 className="typography-h2 font-bold text-gray-900 mb-3 font-['Open_Sans']">
                Overall Capability of Tenant Billing Solution
              </h2>
              <p className="typography-body text-gray-700 max-w-2xl mx-auto font-medium leading-relaxed font-['Open_Sans']">
                A complete platform for multi-tenant utility billing, designed for accuracy, automation, and transparency.
              </p>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="bg-green-50 border border-green-200 rounded-xl p-6 shadow-sm">
                <h3 className="font-bold text-2xl text-green-800 mb-2">Accurate Billing</h3>
                <p className="text-gray-900 text-lg font-['Open_Sans']">Automated and precise allocation of utility costs for each tenant.</p>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-xl p-6 shadow-sm">
                <h3 className="font-bold text-2xl text-green-800 mb-2">Automated Invoicing</h3>
                <p className="text-gray-700 text-lg font-['Open_Sans']">Generate and send invoices automatically, reducing manual work.</p>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-xl p-6 shadow-sm">
                <h3 className="font-bold text-2xl text-green-800 mb-2">Real-Time Monitoring</h3>
                <p className="text-gray-700 text-lg font-['Open_Sans']">Track utility usage and billing status in real time.</p>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-xl p-6 shadow-sm">
                <h3 className="font-bold text-2xl text-green-800 mb-2">Transparent Reporting</h3>
                <p className="text-gray-700 text-lg font-['Open_Sans']">Access clear, detailed reports for tenants and property managers.</p>
              </div>
            </div>
          </div>
        </div>
        {/* End of Overall Capability Section */}

        {/* Tenant Portal Section - Modern Horizontal Layout (expanded, image URL instead of image) */}
         <div className="py-12 sm:py-16 bg-white border-t border-green-100">
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          
          {/* Image Section - Bottom on mobile/tablet, right on desktop */}
          <div className="flex justify-center lg:justify-end w-full order-2 lg:order-2">
            <div className="w-full max-w-lg lg:max-w-none">
              <img
                src="/Tenant Billing/2 Dashboard.png"
                alt="Tenant Portal Dashboard"
                className="rounded-2xl shadow-2xl border-2 border-green-200 w-full object-cover bg-white"
                style={{ background: 'linear-gradient(135deg, #e0f7ef 0%, #f0fdfa 100%)' }}
                loading="lazy"
              />
            </div>
          </div>

          {/* Content Section - Top on mobile/tablet, left on desktop */}
          <div className="space-y-6 text-center lg:text-left order-1 lg:order-1">
            
            {/* Badge */}
            <div className="flex justify-center lg:justify-start">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 border border-green-200">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <span className="text-green-700 font-bold text-sm">AI-Powered Solution</span>
              </div>
            </div>
            
            {/* Heading */}
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-black text-black leading-tight">
              AI Self-Care <span className="text-green-600">Tenant Portal</span>
            </h2>
            
            {/* Subheading */}
            <p className="text-base lg:text-lg text-black font-bold">
              Give tenants control over their costs and consumption with our comprehensive self-care portal solution.
            </p>
            
            {/* Features List */}
            <ul className="space-y-4 mt-8">
              <li className="flex items-start text-left">
                <div className="flex-shrink-0 bg-green-500 text-white rounded-lg p-2 mr-4 mt-0.5">
                  <svg width="16" height="16" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" strokeWidth="2" d="M5 13l4 4L19 7"/>
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="font-bold text-black mb-1">Mobile App & Web Portal</div>
                  <div className="text-black text-sm leading-relaxed">
                    Access consumption data, view bills, and manage payments from any device with our intuitive interface.
                  </div>
                </div>
              </li>
              
              <li className="flex items-start text-left">
                <div className="flex-shrink-0 bg-green-500 text-white rounded-lg p-2 mr-4 mt-0.5">
                  <svg width="16" height="16" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" strokeWidth="2" d="M5 13l4 4L19 7"/>
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="font-bold text-black mb-1">Customizable Dashboard</div>
                  <div className="text-black text-sm leading-relaxed">
                    Personalized insights dashboard showing consumption patterns, cost breakdowns, and savings opportunities.
                  </div>
                </div>
              </li>
              
              <li className="flex items-start text-left">
                <div className="flex-shrink-0 bg-green-500 text-white rounded-lg p-2 mr-4 mt-0.5">
                  <svg width="16" height="16" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" strokeWidth="2" d="M5 13l4 4L19 7"/>
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="font-bold text-black mb-1">24/7 AI Chatbot Support</div>
                  <div className="text-black text-sm leading-relaxed">
                    Intelligent AI assistant to handle inquiries, explain billing details, and help with requests without human intervention.
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

        {/* Modern Contact Section */}
        <div className="mt-12 mb-16 py-12 sm:py-16 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 relative overflow-hidden">
          {/* Subtle Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-5 left-5 w-32 h-32 bg-gradient-to-br from-green-200/20 to-emerald-300/15 rounded-full blur-2xl"></div>
            <div className="absolute bottom-5 right-5 w-32 h-32 bg-gradient-to-br from-emerald-200/15 to-teal-300/10 rounded-full blur-2xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            {/* Modern Compact Heading */}
            <h2 className="text-xl sm:text-2xl lg:text-3xl font-black text-gray-900 mb-3 sm:mb-4 font-['Open_Sans']">
              Need a Custom <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">Tenant Billing Solution?</span>
            </h2>

            {/* Compact Description */}
            <p className="text-sm sm:text-base text-gray-700 max-w-2xl mx-auto leading-relaxed mb-6 font-['Open_Sans']">
              Our billing specialists can design systems to your specific requirements with industry-leading
              accuracy and performance. Get in touch today to discuss your tenant billing needs.
            </p>

            {/* Modern Compact Button */}
            <button
              onClick={() => navigate('/contact/sales')}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold text-sm rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 font-['Open_Sans']"
            >
              <Users className="w-4 h-4 mr-2" />
              Contact Support
            </button>
          </div>
        </div>

        {/* Custom Animations */}
        <style>{`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(3deg); }
          }
          @keyframes float-delayed {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-12px) rotate(-2deg); }
          }
          .animate-float {
            animation: float 5s ease-in-out infinite;
          }
          .animate-float-delayed {
            animation: float-delayed 6s ease-in-out infinite;
          }
          html {
            font-size: 16px;
          }
          body, .font-['Open_Sans'] {
            font-size: 1rem;
          }
          h1, h2, h3, h4, h5, h6 {
            line-height: 1.2;
          }
          @media (max-width: 640px) {
            html { font-size: 15px; }
            h1, h2, h3, h4, h5, h6 { font-size: 1.1em; }
          }
        `}</style>
      </PageLayout>
    </div>
  );
};

export default TenantBillingSolutionPage;