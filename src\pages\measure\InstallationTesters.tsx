import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowRight,
  Zap,
  Shield,
  Gauge,
  FileText,
  Menu,
  X,
  Star
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';

const PDF_URL = '/T&M April 2025.pdf';

const products = [
  {
    id: 'ca6117',
    model: 'CA 6117',
    subtitle: 'Advanced Installation Tester',
    image: '/installation testers/CA 6117.png',
    display: '5.7" backlit color LCD',
    features: [
      'All-in-one installation tester',
      'All neutral systems (TT, TN, IT)',
      '1000 memory locations',
    ],
    voltage: '550 VAC/DC',
    measurement: 'Comprehensive',
    accuracy: 'High',
  },
  {
    id: 'ca6133',
    model: 'CA 6133',
    subtitle: 'Standard Installation Tester',
    image: '/installation testers/CA 6133-1.png',
    display: '231-segment LCD with backlighting',
    features: [
      'Android app for report generation',
      'Automatic test sequences',
      'Multiple power supply options',
    ],
    voltage: 'Up to 550 VAC & 800.0 VDC',
    measurement: 'Standard',
    accuracy: 'Standard',
  }
];

const tabs = [
  { id: 'overview', label: 'Overview', icon: Gauge },
  { id: 'comparison', label: 'Compare', icon: Star }
];

const HeroSection = ({ onRequestDemo, onViewBrochure }) => (
  <section className="relative min-h-[60vh] flex items-center justify-center py-6 md:py-12 overflow-hidden">
    {/* Background Elements */}
    <div className="absolute inset-0 z-0">
      <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
      <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      <div className="absolute top-20 right-20 w-32 h-32 bg-yellow-300 rounded-full opacity-20"></div>
    </div>
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 flex flex-col items-center justify-center w-full">
      <div className="text-center w-full">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8 flex flex-col items-center justify-center w-full"
        >
          <div className="inline-block bg-yellow-400 px-6 py-3 rounded-full mb-4">
            <span className="text-gray-900 font-bold text-lg" style={{ fontFamily: 'Open Sans, sans-serif' }}>
              KRYKARD Installation Testers
            </span>
          </div>
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-bold text-gray-900 leading-tight mb-4" style={{ fontFamily: 'Open Sans, sans-serif' }}>
            INSTALLATION TESTERS
          </h1>
          <p className="text-base md:text-lg lg:text-lg text-black leading-relaxed font-medium max-w-4xl mx-auto mb-8" style={{ fontFamily: 'Open Sans, sans-serif' }}>
            Professional electrical test and measurement equipment compliant with international standards.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center pt-2">
            <button
              className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base"
              onClick={onRequestDemo}
            >
              <span>Request Demo</span>
              <ArrowRight className="h-5 w-5" />
            </button>
            <button
              className="px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:bg-yellow-50 flex items-center justify-center space-x-3 text-base"
              onClick={onViewBrochure}
            >
              <span>View Brochure</span>
              <FileText className="h-5 w-5" />
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  </section>
);

const FeatureHighlight = ({ icon: Icon, title, description }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6 }}
    viewport={{ once: true }}
    className="rounded-2xl border border-yellow-200 hover:border-yellow-400 transition-all duration-300 p-4 h-full bg-transparent flex flex-col items-center text-center"
    style={{ fontFamily: 'Open Sans, sans-serif' }}
  >
    <div className="flex flex-row items-center gap-3 mb-2 justify-center w-full">
      <div className="bg-gradient-to-br from-yellow-400 to-yellow-500 w-10 h-10 rounded-2xl flex items-center justify-center">
        <Icon className="h-6 w-6 text-gray-900" />
      </div>
      <h3 className="text-base md:text-lg font-bold text-gray-900 m-0 p-0">{title}</h3>
    </div>
    <p className="text-gray-700 font-medium text-sm md:text-base leading-relaxed">{description}</p>
  </motion.div>
);

// Product Overview Card Component (following multimeter design pattern)
const ProductCard = ({
  title,
  modelNumber,
  image,
  displayInfo,
  onViewDetailsClick
}: {
  title: string;
  modelNumber: string;
  image: string;
  displayInfo: string;
  onViewDetailsClick: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white border border-yellow-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 flex flex-col h-full"
      style={{ fontFamily: 'Open Sans, sans-serif' }}
    >
      {/* Model Number Badge */}
      <div className="flex justify-end p-3">
        <span className="bg-yellow-100 text-yellow-800 text-xs font-semibold px-3 py-1 rounded-full">
          {modelNumber}
        </span>
      </div>
      {/* Product Image */}
      <div className="flex items-center justify-center h-32 md:h-40 bg-yellow-50">
        <img
          src={image}
          alt={title}
          className="h-24 md:h-32 object-contain"
        />
      </div>
      <div className="p-4 flex-1 flex flex-col justify-between">
        <div className="text-center mb-2">
          <h3 className="text-base font-bold text-gray-900">{title}</h3>
          <div className="text-xs text-gray-600 mt-1">{displayInfo}</div>
        </div>
        <button
          onClick={onViewDetailsClick}
          className="w-full bg-yellow-300 hover:bg-yellow-400 text-gray-900 font-bold py-2 px-4 rounded-lg transition-all duration-200 mt-4 flex items-center justify-center space-x-2"
        >
          <span>View Details</span>
          <ArrowRight className="inline h-4 w-4 ml-1" />
        </button>
      </div>
    </motion.div>
  );
};

const Navigation = ({ activeTab, setActiveTab, isMobileMenuOpen, setIsMobileMenuOpen }) => (
  <nav className="sticky top-0 z-50 bg-white shadow-lg border-b border-gray-200" style={{ fontFamily: 'Open Sans, sans-serif' }}>
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Desktop Navigation */}
      <div className="hidden md:flex justify-center py-4">
        <div className="bg-gray-100 p-2 rounded-full flex space-x-2">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-6 py-3 font-bold rounded-full transition-all duration-300 flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'bg-yellow-400 text-gray-900 shadow-lg transform -translate-y-0.5'
                  : 'text-gray-600 hover:bg-yellow-50 hover:text-yellow-600'
              }`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="text-base">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>
      {/* Mobile Navigation */}
      <div className="md:hidden flex justify-between items-center py-4">
        <span className="font-bold text-gray-900 text-lg">
          {tabs.find(tab => tab.id === activeTab)?.label}
        </span>
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="text-gray-600 hover:text-yellow-600"
        >
          {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>
      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-white border-t border-gray-200"
          >
            <div className="py-4 space-y-2">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center space-x-3 ${
                    activeTab === tab.id
                      ? 'bg-yellow-100 text-yellow-800 font-bold'
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon className="h-5 w-5" />
                  <span className="text-base">{tab.label}</span>
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  </nav>
);

const ComparisonTable = () => {
  console.log('InstallationTesters ComparisonTable rendering with products:', products.length);

  return (
    <div className="comparison-table bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden w-full" style={{ fontFamily: 'Open Sans, sans-serif', display: 'block', visibility: 'visible', opacity: 1 }}>
      <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 p-6">
        <h3 className="text-xl md:text-2xl font-bold text-center text-gray-900">Model Comparison</h3>
      </div>
      <div className="p-6 overflow-x-auto">
        <table className="min-w-full table-auto border-collapse border border-gray-300">
          <thead>
            <tr className="border-b-2 border-yellow-400">
              <th className="text-left py-4 px-4 font-bold text-gray-900 bg-yellow-50 border border-gray-300">Feature</th>
              {products.map(product => (
                <th key={product.id} className="text-center py-4 px-4 font-bold text-gray-900 bg-yellow-50 min-w-[200px] border border-gray-300">
                  {product.model}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            <tr className="border-b border-gray-300 hover:bg-yellow-50 transition-colors">
              <td className="py-4 px-4 font-semibold text-gray-900 bg-gray-50 border border-gray-300">Display</td>
              {products.map(product => (
                <td key={product.id} className="py-4 px-4 text-center font-medium text-gray-700 border border-gray-300">
                  {product.display}
                </td>
              ))}
            </tr>
            <tr className="border-b border-gray-300 hover:bg-yellow-50 transition-colors">
              <td className="py-4 px-4 font-semibold text-gray-900 bg-gray-50 border border-gray-300">Voltage Range</td>
              {products.map(product => (
                <td key={product.id} className="py-4 px-4 text-center font-medium text-gray-700 border border-gray-300">
                  {product.voltage}
                </td>
              ))}
            </tr>
            <tr className="border-b border-gray-300 hover:bg-yellow-50 transition-colors">
              <td className="py-4 px-4 font-semibold text-gray-900 bg-gray-50 border border-gray-300">Measurement</td>
              {products.map(product => (
                <td key={product.id} className="py-4 px-4 text-center font-medium text-gray-700 border border-gray-300">
                  {product.measurement}
                </td>
              ))}
            </tr>
            <tr className="hover:bg-yellow-50 transition-colors">
              <td className="py-4 px-4 font-semibold text-gray-900 bg-gray-50 border border-gray-300">Accuracy</td>
              {products.map(product => (
                <td key={product.id} className="py-4 px-4 text-center font-medium text-gray-700 border border-gray-300">
                  {product.accuracy}
                </td>
              ))}
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

const InstallationTesters = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('overview');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tab = searchParams.get('tab');
    if (tab) setActiveTab(tab);
  }, [location.search]);

  const handleViewDetails = (id: string) => {
    navigate(`/measure/installation-testers/product/${id}`);
  };
  const handleRequestDemo = () => {
    navigate('/contact/sales');
  };
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      <HeroSection onRequestDemo={handleRequestDemo} onViewBrochure={handleViewBrochure} />
      <Navigation
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        isMobileMenuOpen={isMobileMenuOpen}
        setIsMobileMenuOpen={setIsMobileMenuOpen}
      />
      {activeTab === 'overview' && (
        <section id="products-section" className="py-12 md:py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-10"
            >
              <div className="inline-block bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                PRODUCTS
              </div>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Our Installation Tester Range
              </h2>
              <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-2">
                Choose the perfect tester for your electrical measurement and compliance needs.
              </p>
            </motion.div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-8">
              {products.map((product) => (
                <ProductCard
                  key={product.id}
                  title={product.subtitle}
                  modelNumber={product.model}
                  image={product.image}
                  displayInfo={product.display}
                  onViewDetailsClick={() => handleViewDetails(product.id)}
                />
              ))}
            </div>
          </div>
        </section>
      )}
      {/* Key Features Section */}
      {activeTab === 'overview' && (
        <section className="py-8 md:py-12 bg-yellow-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-8 lg:px-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-14"
            >
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-gray-900 mb-4 tracking-tight">
                Key Features
              </h2>
              <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto font-medium mb-2">
                Discover the standout features that make our testers the preferred choice for professionals.
              </p>
            </motion.div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 justify-center">
              <FeatureHighlight
                icon={Zap}
                title="All-in-One Testing"
                description="Comprehensive measurement capabilities including insulation, continuity, loop impedance, RCD testing, earth resistance, and more."
              />
              <FeatureHighlight
                icon={Shield}
                title="Compliance Assurance"
                description="Ensures compliance with international standards including IEC 60364-6, NF C 15-100, VDE 100, and FD C 16-600."
              />
              <FeatureHighlight
                icon={Gauge}
                title="Versatile Applications"
                description="Compatible with all neutral system arrangements (TT, TN, IT), ideal for industries, tertiary, and residential."
              />
              <FeatureHighlight
                icon={FileText}
                title="Data Logging & Storage"
                description="Extensive memory for long-term data logging and easy export for analysis."
              />
              <FeatureHighlight
                icon={Menu}
                title="User-Friendly Interface"
                description="Intuitive displays and navigation for quick setup and operation."
              />
              <FeatureHighlight
                icon={Star}
                title="Flexible Power & Connectivity"
                description="Multiple power supply options and PC interface for seamless data transfer."
              />
            </div>
          </div>
        </section>
      )}
      {/* Comparison Section */}
      {activeTab === 'comparison' && (
        <section className="py-12 md:py-16 min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-10"
            >
              <div className="inline-block bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                COMPARISON
              </div>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Compare Our Models
              </h2>
              <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-8">
                Find the perfect tester for your specific requirements
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="w-full"
            >
              <ComparisonTable />
            </motion.div>
            {/* Debug info - remove in production */}
            <div className="mt-8 text-center text-sm text-gray-500">
              Active Tab: {activeTab} | Products: {products.length}
            </div>
          </div>
        </section>
      )}
      {/* Contact Section */}
      <section className="py-12 md:py-16 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200 mt-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Need Expert Advice?
            </h2>
            <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
              Our specialists provide comprehensive guidance on installation testing solutions
            </p>
            <button
              className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto mt-2"
              onClick={handleRequestDemo}
            >
              <span>Contact Sales</span>
              <ArrowRight className="h-5 w-5" />
            </button>
          </motion.div>
        </div>
      </section>

      {/* CSS Override for table borders visibility */}
      <style>{`
        .comparison-table {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }

        .comparison-table table {
          border-collapse: collapse !important;
          border: 2px solid #d1d5db !important;
        }

        .comparison-table table th,
        .comparison-table table td {
          border: 1px solid #d1d5db !important;
          border-collapse: collapse !important;
        }

        .comparison-table table thead tr {
          border-bottom: 3px solid #fbbf24 !important;
        }

        .comparison-table table tbody tr {
          border-bottom: 1px solid #d1d5db !important;
        }
      `}</style>
    </PageLayout>
  );
};

export default InstallationTesters;