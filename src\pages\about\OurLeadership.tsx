import React from 'react';
import Layout from '../../components/layout/Layout';

const leadership = [
  {
    title: 'Chief Executive Officer',
    name: '<PERSON><PERSON><PERSON>',
    photo: '/path/to/ceo-photo.jpg',
    description: 'Visionary leader with 20+ years in the energy sector. Passionate about innovation and excellence.'
  },
  {
    title: 'Director of Operations',
    name: '<PERSON><PERSON>',
    photo: '/path/to/director-photo.jpg',
    description: 'Expert in operational strategy and team building. Drives efficiency and growth.'
  },
  {
    title: 'Vice President, Technology',
    name: '<PERSON><PERSON>',
    photo: '/path/to/vp-photo.jpg',
    description: 'Tech enthusiast focused on digital transformation and product development.'
  },
  {
    title: 'Assistant Vice President, Marketing',
    name: '<PERSON><PERSON><PERSON>',
    photo: '/path/to/avp-photo.jpg',
    description: 'Creative marketer with a knack for brand storytelling and customer engagement.'
  }
];

const OurLeadership: React.FC = () => {
  return (
    <Layout>
      <div style={{ maxWidth: '900px', margin: '0 auto', padding: '3rem 1rem' }}>
        <h1 style={{ color: '#000', fontSize: '2.5rem', fontWeight: 700, marginBottom: '0.5rem', letterSpacing: '-1px', textAlign: 'center' }}>Our Leadership</h1>
        <p style={{ color: '#000', fontSize: '1.1rem', textAlign: 'center', marginBottom: '2.5rem', maxWidth: '600px', marginLeft: 'auto', marginRight: 'auto', lineHeight: 1.6 }}>
          Meet the passionate individuals guiding our vision and driving our success. Our leadership team brings together deep expertise, strategic thinking, and a commitment to excellence. (You can update this content as needed.)
        </p>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2.5rem', justifyContent: 'center' }}>
          {leadership.map((leader, idx) => (
            <div
              key={idx}
              style={{
                background: '#fff',
                border: '2px solid #000',
                borderRadius: '18px',
                padding: '2rem 1.5rem',
                width: '260px',
                minHeight: '370px',
                boxShadow: '0 4px 24px rgba(0,0,0,0.07)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                transition: 'transform 0.2s',
                cursor: 'pointer',
              }}
              onMouseOver={e => (e.currentTarget.style.transform = 'translateY(-6px) scale(1.03)')}
              onMouseOut={e => (e.currentTarget.style.transform = 'none')}
            >
              <div style={{
                width: '110px',
                height: '110px',
                borderRadius: '50%',
                background: '#000',
                marginBottom: '1.2rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
              }}>
                {/* Placeholder for photo, replace src as needed */}
                <img
                  src={leader.photo}
                  alt={leader.name}
                  style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: '50%', filter: 'grayscale(100%)' }}
                  onError={e => (e.currentTarget.style.display = 'none')}
                />
                {/* If no image, show initials */}
                {!leader.photo && (
                  <span style={{ color: '#fff', fontSize: '2.5rem', fontWeight: 700 }}>
                    {leader.name.split(' ').map(n => n[0]).join('')}
                  </span>
                )}
              </div>
              <h2 style={{ color: '#000', fontSize: '1.2rem', fontWeight: 700, margin: '0.5rem 0 0.2rem 0', letterSpacing: '-0.5px', textAlign: 'center' }}>{leader.title}</h2>
              <h3 style={{ color: '#000', fontSize: '1.05rem', fontWeight: 500, margin: '0 0 0.7rem 0', textAlign: 'center', opacity: 0.85 }}>{leader.name}</h3>
              <p style={{ color: '#000', fontSize: '0.98rem', textAlign: 'center', opacity: 0.7, lineHeight: 1.5 }}>{leader.description}</p>
            </div>
          ))}
        </div>
      </div>
    </Layout>
  );
};

export default OurLeadership; 